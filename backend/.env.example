# Database Configuration
DATABASE_URL=sqlite:///./daily_motivator.db
DATABASE_ECHO=false

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME=Daily Motivator API
VERSION=1.0.0
DESCRIPTION=Backend API for Daily Motivator App

# Environment
ENVIRONMENT=development
DEBUG=true

# CORS
BACKEND_CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080", "http://localhost:4200"]

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# File Upload
MAX_FILE_SIZE=5242880  # 5MB
UPLOAD_DIR=./uploads
ALLOWED_EXTENSIONS=["jpg", "jpeg", "png", "gif", "webp"]

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60

# External APIs (if needed)
QUOTES_API_KEY=your-quotes-api-key
UNSPLASH_ACCESS_KEY=your-unsplash-access-key

# Email (if needed for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME=Daily Motivator

# Redis (if needed for caching)
REDIS_URL=redis://localhost:6379/0

# Monitoring
SENTRY_DSN=your-sentry-dsn-if-using
