services:
  - type: web
    name: motivational-quotes-api
    env: python
    buildCommand: |
      pip install --upgrade pip
      pip install -r requirements.txt
      python scripts/migrate_to_postgres.py
    startCommand: uvicorn app.main:app --host 0.0.0.0 --port $PORT
    envVars:
      - key: ENVIRONMENT
        value: production
      - key: DATABASE_ECHO
        value: false
      - key: LOG_LEVEL
        value: INFO
      - key: LOG_FORMAT
        value: json
      - key: SECRET_KEY
        generateValue: true
      - key: DATABASE_URL
        fromDatabase:
          name: motivational-quotes-db
          property: connectionString
    healthCheckPath: /health

databases:
  - name: motivational-quotes-db
    databaseName: motivational_quotes
    user: motivational_user
