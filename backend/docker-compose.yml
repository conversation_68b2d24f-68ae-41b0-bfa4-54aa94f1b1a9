version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: motivational_quotes
      POSTGRES_USER: motivational_user
      POSTGRES_PASSWORD: motivational_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U motivational_user -d motivational_quotes"]
      interval: 10s
      timeout: 5s
      retries: 5

  # API Service
  api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=************************************************************/motivational_quotes
      - SECRET_KEY=dev-secret-key-change-in-production
      - JWT_SECRET=dev-jwt-secret-change-in-production
    depends_on:
      db:
        condition: service_healthy
    volumes:
      - .:/app
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # Redis (optional for caching)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
