# Production environment configuration for Render.com

# Environment
ENVIRONMENT=production
DEBUG=false

# Database (will be set by Render.com)
# DATABASE_URL will be automatically provided by Render.com PostgreSQL service

# Security
SECRET_KEY=your-super-secret-key-here-change-this-in-production
JWT_SECRET=your-jwt-secret-key-here-change-this-in-production
JWT_EXPIRES_IN=7d

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME=Daily Motivator API
VERSION=1.0.0

# CORS
BACKEND_CORS_ORIGINS=["https://your-frontend-domain.com"]

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Database settings
DATABASE_ECHO=false

# Rate limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=60

# Email (optional - for user notifications)
SMTP_TLS=true
SMTP_PORT=587
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# File upload limits
MAX_UPLOAD_SIZE=10485760  # 10MB

# Cache settings
REDIS_URL=  # Optional Redis for caching

# Monitoring
SENTRY_DSN=  # Optional Sentry for error tracking
