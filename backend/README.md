# Daily Motivator Backend API

A comprehensive Python backend API for the Daily Motivator app, built with FastAPI, SQLAlchemy, and SQLite.

## 🚀 Features

- **RESTful API** with FastAPI
- **SQLite Database** with SQLAlchemy ORM
- **JWT Authentication** with secure password hashing
- **User Management** with profiles, preferences, and gamification
- **Quote Management** with categories, search, and interactions
- **Analytics & Tracking** for user behavior and app metrics
- **Configurable Settings** with environment variables
- **Comprehensive Validation** with Pydantic schemas
- **Rate Limiting** and security middleware
- **Health Checks** and monitoring endpoints
- **Auto-generated Documentation** with Swagger/OpenAPI

## 📁 Project Structure

```
backend/
├── app/
│   ├── api/                    # API endpoints
│   │   └── v1/
│   │       ├── endpoints/      # Route handlers
│   │       └── api.py         # API router configuration
│   ├── core/                   # Core functionality
│   │   ├── config.py          # Configuration settings
│   │   ├── database.py        # Database setup and connection
│   │   └── security.py        # Authentication and security
│   ├── models/                 # SQLAlchemy models
│   │   ├── user.py            # User model
│   │   ├── quote.py           # Quote model
│   │   ├── category.py        # Category model
│   │   ├── user_preference.py # User preferences
│   │   └── analytics.py       # Analytics models
│   ├── schemas/                # Pydantic schemas
│   │   ├── user.py            # User schemas
│   │   ├── quote.py           # Quote schemas
│   │   ├── category.py        # Category schemas
│   │   └── analytics.py       # Analytics schemas
│   ├── services/               # Business logic layer
│   │   ├── user_service.py    # User operations
│   │   ├── quote_service.py   # Quote operations
│   │   └── analytics_service.py # Analytics operations
│   └── main.py                # FastAPI application
├── scripts/
│   └── init_db.py             # Database initialization
├── requirements.txt           # Python dependencies
├── .env.example              # Environment variables template
└── README.md                 # This file
```

## 🛠️ Setup and Installation

### Prerequisites

- Python 3.8+
- pip (Python package manager)

### 1. Clone and Navigate

```bash
cd backend
```

### 2. Create Virtual Environment

```bash
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate
```

### 3. Install Dependencies

```bash
pip install -r requirements.txt
```

### 4. Environment Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your settings
nano .env
```

### 5. Initialize Database

```bash
python scripts/init_db.py
```

### 6. Run the Application

```bash
# Development mode
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Or using the main.py directly
python app/main.py
```

The API will be available at:
- **API**: http://localhost:8000
- **Documentation**: http://localhost:8000/api/v1/docs
- **Health Check**: http://localhost:8000/health

## 🔧 Configuration

### Environment Variables

Key configuration options in `.env`:

```env
# Database
DATABASE_URL=sqlite:///./daily_motivator.db
DATABASE_ECHO=false

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# API
API_V1_STR=/api/v1
PROJECT_NAME=Daily Motivator API
ENVIRONMENT=development
DEBUG=true

# CORS
BACKEND_CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]
```

### Database Configuration

The application uses SQLite by default, but can be configured for other databases:

```python
# PostgreSQL
DATABASE_URL=postgresql://user:password@localhost/daily_motivator

# MySQL
DATABASE_URL=mysql://user:password@localhost/daily_motivator
```

## 📚 API Documentation

### Authentication

The API uses JWT (JSON Web Tokens) for authentication:

```bash
# Register a new user
POST /api/v1/auth/register

# Login
POST /api/v1/auth/login

# Get current user info
GET /api/v1/auth/me
```

### Sample API Calls

```bash
# Register user
curl -X POST "http://localhost:8000/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "username": "testuser",
    "password": "password123",
    "full_name": "Test User"
  }'

# Login
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=testuser&password=password123"

# Get random quote
curl -X GET "http://localhost:8000/api/v1/quotes/random"

# Get quotes with authentication
curl -X GET "http://localhost:8000/api/v1/quotes/" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Main Endpoints

#### Authentication (`/api/v1/auth/`)
- `POST /register` - Register new user
- `POST /login` - User login
- `POST /logout` - User logout
- `GET /me` - Get current user info
- `POST /password-reset` - Request password reset
- `POST /verify-email` - Verify email address

#### Users (`/api/v1/users/`)
- `GET /` - List users (admin only)
- `GET /{user_id}` - Get user by ID
- `PUT /{user_id}` - Update user
- `DELETE /{user_id}` - Delete user

#### Quotes (`/api/v1/quotes/`)
- `GET /` - List quotes with filtering
- `GET /random` - Get random quote
- `GET /{quote_id}` - Get specific quote
- `POST /` - Create quote (admin only)
- `PUT /{quote_id}` - Update quote (admin only)
- `POST /{quote_id}/interact` - Record interaction

#### Categories (`/api/v1/categories/`)
- `GET /` - List categories
- `GET /{category_id}` - Get category
- `POST /` - Create category (admin only)
- `PUT /{category_id}` - Update category (admin only)

#### Analytics (`/api/v1/analytics/`)
- `GET /dashboard` - Dashboard statistics
- `GET /user/{user_id}` - User statistics
- `POST /track` - Track custom metric

## 🗄️ Database Schema

### Core Models

#### User
- Authentication and profile information
- Gamification (points, levels, streaks)
- Preferences and settings

#### Quote
- Quote text, author, source
- Categorization and tags
- Engagement metrics (views, likes, shares)

#### Category
- Quote organization
- Display properties (color, icon)
- Statistics

#### Analytics
- User activity tracking
- Quote interactions
- General metrics

### Relationships

```
User (1) ←→ (N) UserPreference
User (1) ←→ (N) UserActivity
User (1) ←→ (N) QuoteInteraction

Category (1) ←→ (N) Quote
Quote (1) ←→ (N) QuoteInteraction

User (1) ←→ (N) Analytics
```

## 🔒 Security Features

- **Password Hashing** with bcrypt
- **JWT Authentication** with configurable expiration
- **Rate Limiting** on sensitive endpoints
- **Input Validation** with Pydantic
- **SQL Injection Protection** with SQLAlchemy ORM
- **CORS Configuration** for cross-origin requests
- **Security Headers** for HTTP responses

## 📊 Monitoring and Health Checks

### Health Check Endpoint

```bash
GET /health
```

Returns:
```json
{
  "status": "healthy",
  "database": "connected",
  "version": "1.0.0",
  "environment": "development"
}
```

### Metrics Endpoint

```bash
GET /metrics
```

Basic application metrics for monitoring.

## 🧪 Testing

### Sample Users

The initialization script creates sample users:

- **Admin**: `<EMAIL>` / `admin123`
- **Demo**: `<EMAIL>` / `demo123`
- **John**: `<EMAIL>` / `password123`

### Testing API Endpoints

Use the interactive documentation at `/api/v1/docs` to test endpoints directly in your browser.

## 🚀 Deployment

### Production Considerations

1. **Environment Variables**:
   ```env
   ENVIRONMENT=production
   DEBUG=false
   SECRET_KEY=your-production-secret-key
   ```

2. **Database**: Consider PostgreSQL or MySQL for production
3. **Reverse Proxy**: Use nginx or similar
4. **SSL/TLS**: Enable HTTPS
5. **Monitoring**: Set up logging and monitoring
6. **Backup**: Regular database backups

### Docker Deployment

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License.

## 🆘 Support

For issues and questions:
1. Check the API documentation at `/api/v1/docs`
2. Review the health check endpoint at `/health`
3. Check application logs
4. Verify database connectivity

---

*Built with ❤️ using FastAPI, SQLAlchemy, and modern Python practices.*
