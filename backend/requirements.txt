# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Database
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9
asyncpg==0.29.0

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Environment & Configuration
python-dotenv==1.0.0
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP & API
httpx==0.25.2
requests==2.31.0

# Data Validation & Serialization
email-validator==2.1.0
python-dateutil==2.8.2

# Development & Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
black==23.11.0
flake8==6.1.0
isort==5.12.0

# Logging & Monitoring
structlog==23.2.0
rich==13.7.0

# CORS & Middleware
# CORS is built into FastAPI

# File Handling
aiofiles==23.2.1
pillow==10.1.0

# Utilities
click==8.1.7
python-slugify==8.0.1
