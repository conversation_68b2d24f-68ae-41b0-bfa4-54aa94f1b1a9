#!/usr/bin/env python3
"""
Development server runner for Daily Motivator API.
"""

import uvicorn
import sys
import os
from pathlib import Path

# Add the current directory to Python path
sys.path.append(str(Path(__file__).parent))

from app.core.config import settings


def main():
    """Run the development server."""
    print(f"🚀 Starting {settings.PROJECT_NAME}")
    print(f"📍 Environment: {settings.ENVIRONMENT}")
    print(f"🔧 Debug mode: {settings.DEBUG}")
    print(f"🌐 API docs: http://localhost:8000{settings.API_V1_STR}/docs")
    print(f"❤️  Health check: http://localhost:8000/health")
    print("-" * 50)
    
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.is_development,
        log_level=settings.LOG_LEVEL.lower(),
        access_log=True,
        reload_dirs=["app"] if settings.is_development else None,
    )


if __name__ == "__main__":
    main()
