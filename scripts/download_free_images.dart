#!/usr/bin/env dart

import 'dart:io';
import 'dart:convert';
import 'package:http/http.dart' as http;

/// <PERSON>ript to download free, copyright-free images for the motivational app
/// Uses Lorem Picsum (free placeholder images) and Unsplash (with proper attribution)
class ImageDownloader {
  static const String assetsPath = 'assets/images';
  
  // Free image sources
  static const String loremPicsumBase = 'https://picsum.photos';
  static const String unsplashBase = 'https://source.unsplash.com';
  
  // Image categories and their keywords
  static const Map<String, List<String>> imageCategories = {
    'backgrounds': [
      'gradient', 'texture', 'pattern', 'minimal', 'abstract',
      'geometric', 'watercolor', 'marble', 'paper', 'fabric'
    ],
    'motivational': [
      'mountain', 'sunrise', 'success', 'achievement', 'goal',
      'inspiration', 'motivation', 'growth', 'progress', 'victory',
      'summit', 'peak', 'climb', 'journey', 'path'
    ],
    'nature': [
      'forest', 'ocean', 'landscape', 'tree', 'flower',
      'sky', 'clouds', 'river', 'lake', 'sunset',
      'beach', 'meadow', 'garden', 'waterfall', 'valley'
    ],
    'abstract': [
      'geometric', 'pattern', 'texture', 'gradient', 'minimal',
      'modern', 'design', 'art', 'creative', 'colorful',
      'shapes', 'lines', 'curves', 'digital', 'artistic'
    ],
    'lifestyle': [
      'workspace', 'coffee', 'book', 'journal', 'meditation',
      'yoga', 'fitness', 'healthy', 'balance', 'wellness',
      'tea', 'reading', 'writing', 'peaceful', 'calm'
    ],
    'achievements': [
      'trophy', 'medal', 'success', 'winner', 'celebration',
      'achievement', 'goal', 'target', 'victory', 'champion',
      'award', 'prize', 'accomplishment', 'milestone', 'triumph'
    ],
  };

  static Future<void> main(List<String> args) async {
    print('🎨 Starting free image download for Daily Motivator...\n');
    
    final downloader = ImageDownloader();
    await downloader.downloadAllImages();
    
    print('\n✅ Image download completed successfully!');
    print('📁 Images saved to: $assetsPath');
    print('🎯 Ready to use in your Flutter app!');
  }

  Future<void> downloadAllImages() async {
    // Create directories
    await _createDirectories();
    
    // Download images for each category
    for (final entry in imageCategories.entries) {
      final category = entry.key;
      final keywords = entry.value;
      
      print('📂 Downloading images for category: $category');
      await _downloadCategoryImages(category, keywords);
      print('');
    }
    
    // Download some high-quality background images
    await _downloadHighQualityBackgrounds();
    
    // Create image manifest
    await _createImageManifest();
  }

  Future<void> _createDirectories() async {
    for (final category in imageCategories.keys) {
      final dir = Directory('$assetsPath/$category');
      if (!await dir.exists()) {
        await dir.create(recursive: true);
        print('📁 Created directory: ${dir.path}');
      }
    }
  }

  Future<void> _downloadCategoryImages(String category, List<String> keywords) async {
    final dir = Directory('$assetsPath/$category');
    
    for (int i = 0; i < keywords.length && i < 10; i++) {
      final keyword = keywords[i];
      
      try {
        // Use different sources for variety
        String imageUrl;
        String fileName;
        
        if (i % 3 == 0) {
          // Use Lorem Picsum for some images (completely free)
          imageUrl = '$loremPicsumBase/800/600?random=${category}_$i';
          fileName = '${category}_picsum_$i.jpg';
        } else {
          // Use Unsplash Source for others (free with attribution)
          imageUrl = '$unsplashBase/800x600/?$keyword';
          fileName = '${category}_${keyword}_$i.jpg';
        }
        
        await _downloadImage(imageUrl, '${dir.path}/$fileName');
        print('  ✓ Downloaded: $fileName');
        
        // Add delay to be respectful to free services
        await Future.delayed(const Duration(milliseconds: 500));
        
      } catch (e) {
        print('  ❌ Failed to download image for $keyword: $e');
      }
    }
  }

  Future<void> _downloadHighQualityBackgrounds() async {
    print('🎨 Downloading high-quality background images...');
    
    final backgroundsDir = Directory('$assetsPath/backgrounds');
    
    // Download some beautiful gradient and texture backgrounds
    final backgroundUrls = [
      'https://picsum.photos/1200/800?random=bg1',
      'https://picsum.photos/1200/800?random=bg2',
      'https://picsum.photos/1200/800?random=bg3',
      'https://picsum.photos/1200/800?random=bg4',
      'https://picsum.photos/1200/800?random=bg5',
    ];
    
    for (int i = 0; i < backgroundUrls.length; i++) {
      try {
        final fileName = 'background_hq_$i.jpg';
        await _downloadImage(backgroundUrls[i], '${backgroundsDir.path}/$fileName');
        print('  ✓ Downloaded: $fileName');
        
        await Future.delayed(const Duration(milliseconds: 500));
      } catch (e) {
        print('  ❌ Failed to download background $i: $e');
      }
    }
  }

  Future<void> _downloadImage(String url, String filePath) async {
    final response = await http.get(Uri.parse(url));
    
    if (response.statusCode == 200) {
      final file = File(filePath);
      await file.writeAsBytes(response.bodyBytes);
    } else {
      throw Exception('Failed to download image: ${response.statusCode}');
    }
  }

  Future<void> _createImageManifest() async {
    print('📋 Creating image manifest...');
    
    final manifest = <String, dynamic>{
      'version': '1.0.0',
      'generated': DateTime.now().toIso8601String(),
      'categories': <String, dynamic>{},
      'attribution': {
        'lorem_picsum': {
          'name': 'Lorem Picsum',
          'url': 'https://picsum.photos',
          'license': 'Free to use',
          'description': 'Beautiful placeholder images'
        },
        'unsplash': {
          'name': 'Unsplash',
          'url': 'https://unsplash.com',
          'license': 'Unsplash License',
          'description': 'Beautiful free photos'
        }
      }
    };
    
    // Scan each category directory
    for (final category in imageCategories.keys) {
      final dir = Directory('$assetsPath/$category');
      final images = <Map<String, dynamic>>[];
      
      if (await dir.exists()) {
        final files = await dir.list().toList();
        
        for (final file in files) {
          if (file is File && _isImageFile(file.path)) {
            final fileName = file.path.split('/').last;
            final stat = await file.stat();
            
            images.add({
              'fileName': fileName,
              'path': 'assets/images/$category/$fileName',
              'size': stat.size,
              'created': stat.modified.toIso8601String(),
              'source': fileName.contains('picsum') ? 'lorem_picsum' : 'unsplash',
            });
          }
        }
      }
      
      manifest['categories'][category] = {
        'count': images.length,
        'images': images,
      };
    }
    
    // Save manifest
    final manifestFile = File('$assetsPath/manifest.json');
    await manifestFile.writeAsString(
      const JsonEncoder.withIndent('  ').convert(manifest)
    );
    
    print('✓ Image manifest created: ${manifestFile.path}');
    
    // Print summary
    print('\n📊 Download Summary:');
    for (final entry in (manifest['categories'] as Map<String, dynamic>).entries) {
      final category = entry.key;
      final data = entry.value as Map<String, dynamic>;
      print('  $category: ${data['count']} images');
    }
  }

  bool _isImageFile(String path) {
    final extension = path.toLowerCase().split('.').last;
    return ['jpg', 'jpeg', 'png', 'gif', 'webp'].contains(extension);
  }
}

void main(List<String> args) async {
  await ImageDownloader.main(args);
}
