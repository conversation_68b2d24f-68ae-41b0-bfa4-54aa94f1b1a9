import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/asset_models.dart';
import 'logging_service.dart';

/// Service for managing local assets, downloading free images, and organizing content
class AssetManagementService {
  static const String _assetsVersionKey = 'assets_version';
  static const String _currentAssetsVersion = '1.0.0';
  
 
  
  // Local directories
  late Directory _imagesDir;
  late Directory _backgroundsDir;
  late Directory _categoriesDir;
  late Directory _achievementsDir;
  late Directory _avatarsDir;
  
  // Asset catalogs
  final Map<AssetCategory, List<LocalAsset>> _assetCatalog = {};
  bool _isInitialized = false;

  /// Initialize the asset management service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      await _createDirectories();
      await _loadAssetCatalog();
      await _checkAndUpdateAssets();
      _isInitialized = true;
      
      LoggingService.info('AssetManagementService initialized successfully');
    } catch (e, stackTrace) {
      LoggingService.error(
        'Failed to initialize AssetManagementService: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Create necessary directories for asset storage
  Future<void> _createDirectories() async {
    final appDir = await getApplicationDocumentsDirectory();
    final assetsDir = Directory('${appDir.path}/assets');
    
    _imagesDir = Directory('${assetsDir.path}/images');
    _backgroundsDir = Directory('${assetsDir.path}/backgrounds');
    _categoriesDir = Directory('${assetsDir.path}/categories');
    _achievementsDir = Directory('${assetsDir.path}/achievements');
    _avatarsDir = Directory('${assetsDir.path}/avatars');
    
    for (final dir in [_imagesDir, _backgroundsDir, _categoriesDir, _achievementsDir, _avatarsDir]) {
      if (!await dir.exists()) {
        await dir.create(recursive: true);
      }
    }
  }

  /// Load asset catalog from local storage
  Future<void> _loadAssetCatalog() async {
    for (final category in AssetCategory.values) {
      _assetCatalog[category] = await _loadAssetsForCategory(category);
    }
  }

  /// Load assets for a specific category
  Future<List<LocalAsset>> _loadAssetsForCategory(AssetCategory category) async {
    final dir = _getDirectoryForCategory(category);
    final assets = <LocalAsset>[];
    
    if (await dir.exists()) {
      final files = await dir.list().toList();
      for (final file in files) {
        if (file is File && _isImageFile(file.path)) {
          final asset = LocalAsset(
            id: _generateAssetId(file.path),
            category: category,
            localPath: file.path,
            fileName: file.path.split('/').last,
            fileSize: await file.length(),
            createdAt: (await file.stat()).modified,
          );
          assets.add(asset);
        }
      }
    }
    
    return assets;
  }

  /// Check if assets need updating and download if necessary
  Future<void> _checkAndUpdateAssets() async {
    final prefs = await SharedPreferences.getInstance();
    final currentVersion = prefs.getString(_assetsVersionKey);
    
    if (currentVersion != _currentAssetsVersion) {
      await _downloadDefaultAssets();
      await prefs.setString(_assetsVersionKey, _currentAssetsVersion);
    }
  }

  /// Download default free images from Unsplash
  Future<void> _downloadDefaultAssets() async {
    LoggingService.info('Downloading default assets...');
    
    // Define image collections for each category
    final collections = {
      AssetCategory.motivational: [
        'mountain', 'sunrise', 'success', 'achievement', 'goal',
        'inspiration', 'motivation', 'growth', 'progress', 'victory'
      ],
      AssetCategory.nature: [
        'forest', 'ocean', 'landscape', 'tree', 'flower',
        'sky', 'clouds', 'river', 'mountain', 'sunset'
      ],
      AssetCategory.abstract: [
        'geometric', 'pattern', 'texture', 'gradient', 'minimal',
        'modern', 'design', 'art', 'creative', 'colorful'
      ],
      AssetCategory.lifestyle: [
        'workspace', 'coffee', 'book', 'journal', 'meditation',
        'yoga', 'fitness', 'healthy', 'balance', 'wellness'
      ],
    };

    for (final entry in collections.entries) {
      final category = entry.key;
      final keywords = entry.value;
      
      for (int i = 0; i < keywords.length && i < 5; i++) {
        try {
          await _downloadImageFromUnsplash(keywords[i], category, i);
          // Add delay to respect API rate limits
          await Future.delayed(const Duration(milliseconds: 500));
        } catch (e) {
          LoggingService.warning('Failed to download image for ${keywords[i]}: $e');
        }
      }
    }
  }

  /// Download a single image from Unsplash
  Future<void> _downloadImageFromUnsplash(String query, AssetCategory category, int index) async {
    try {
      // For demo purposes, we'll use placeholder images instead of actual Unsplash API
      // In production, you would use the actual Unsplash API with proper authentication
      final imageUrl = 'https://picsum.photos/800/600?random=$index';
      
      final response = await http.get(Uri.parse(imageUrl));
      if (response.statusCode == 200) {
        final dir = _getDirectoryForCategory(category);
        final fileName = '${query}_$index.jpg';
        final file = File('${dir.path}/$fileName');
        
        await file.writeAsBytes(response.bodyBytes);
        
        final asset = LocalAsset(
          id: _generateAssetId(file.path),
          category: category,
          localPath: file.path,
          fileName: fileName,
          fileSize: response.bodyBytes.length,
          createdAt: DateTime.now(),
          metadata: AssetMetadata(
            title: query.replaceAll('_', ' ').toUpperCase(),
            description: 'Free image from Lorem Picsum',
            tags: [query, category.name],
            source: 'Lorem Picsum',
            license: 'Free to use',
          ),
        );
        
        _assetCatalog[category]?.add(asset);
        
        LoggingService.info('Downloaded image: $fileName');
      }
    } catch (e) {
      LoggingService.error('Failed to download image for $query: $e');
    }
  }

  /// Get directory for a specific asset category
  Directory _getDirectoryForCategory(AssetCategory category) {
    switch (category) {
      case AssetCategory.backgrounds:
        return _backgroundsDir;
      case AssetCategory.motivational:
      case AssetCategory.nature:
      case AssetCategory.abstract:
      case AssetCategory.lifestyle:
        return _categoriesDir;
      case AssetCategory.achievements:
        return _achievementsDir;
      case AssetCategory.avatars:
        return _avatarsDir;
    }
  }

  /// Check if file is an image
  bool _isImageFile(String path) {
    final extension = path.toLowerCase().split('.').last;
    return ['jpg', 'jpeg', 'png', 'gif', 'webp'].contains(extension);
  }

  /// Generate unique asset ID
  String _generateAssetId(String path) {
    return path.hashCode.abs().toString();
  }

  /// Get assets for a specific category
  List<LocalAsset> getAssetsForCategory(AssetCategory category) {
    return _assetCatalog[category] ?? [];
  }

  /// Get all assets
  Map<AssetCategory, List<LocalAsset>> getAllAssets() {
    return Map.from(_assetCatalog);
  }

  /// Get random asset from category
  LocalAsset? getRandomAsset(AssetCategory category) {
    final assets = _assetCatalog[category];
    if (assets == null || assets.isEmpty) return null;
    
    assets.shuffle();
    return assets.first;
  }

  /// Search assets by query
  List<LocalAsset> searchAssets(String query) {
    final results = <LocalAsset>[];
    final lowerQuery = query.toLowerCase();
    
    for (final assets in _assetCatalog.values) {
      for (final asset in assets) {
        if (asset.fileName.toLowerCase().contains(lowerQuery) ||
            asset.metadata?.title?.toLowerCase().contains(lowerQuery) == true ||
            asset.metadata?.tags?.any((tag) => tag.toLowerCase().contains(lowerQuery)) == true) {
          results.add(asset);
        }
      }
    }
    
    return results;
  }

  /// Add custom asset
  Future<LocalAsset> addCustomAsset(
    String sourcePath,
    AssetCategory category, {
    AssetMetadata? metadata,
  }) async {
    final sourceFile = File(sourcePath);
    if (!await sourceFile.exists()) {
      throw Exception('Source file does not exist: $sourcePath');
    }
    
    final dir = _getDirectoryForCategory(category);
    final fileName = '${DateTime.now().millisecondsSinceEpoch}_${sourcePath.split('/').last}';
    final targetFile = File('${dir.path}/$fileName');
    
    await sourceFile.copy(targetFile.path);
    
    final asset = LocalAsset(
      id: _generateAssetId(targetFile.path),
      category: category,
      localPath: targetFile.path,
      fileName: fileName,
      fileSize: await targetFile.length(),
      createdAt: DateTime.now(),
      metadata: metadata,
    );
    
    _assetCatalog[category]?.add(asset);
    
    LoggingService.info('Added custom asset: $fileName');
    return asset;
  }

  /// Delete asset
  Future<void> deleteAsset(LocalAsset asset) async {
    final file = File(asset.localPath);
    if (await file.exists()) {
      await file.delete();
    }
    
    _assetCatalog[asset.category]?.removeWhere((a) => a.id == asset.id);
    LoggingService.info('Deleted asset: ${asset.fileName}');
  }

  /// Get asset file
  File getAssetFile(LocalAsset asset) {
    return File(asset.localPath);
  }

  /// Check if service is initialized
  bool get isInitialized => _isInitialized;
}
