import 'package:google_sign_in/google_sign_in.dart';
import 'local_storage_service.dart';

class GoogleSignInService {
  static GoogleSignInService? _instance;
  late GoogleSignIn _googleSignIn;
  
  GoogleSignInService._() {
    _googleSignIn = GoogleSignIn(
      scopes: [
        'email',
        'profile',
      ],
    );
  }

  static GoogleSignInService get instance {
    _instance ??= GoogleSignInService._();
    return _instance!;
  }

  // Check if user is currently signed in
  Future<bool> isSignedIn() async {
    return await _googleSignIn.isSignedIn();
  }

  // Get current user
  GoogleSignInAccount? get currentUser => _googleSignIn.currentUser;

  // Sign in with Google
  Future<GoogleSignInAccount?> signIn() async {
    try {
      final GoogleSignInAccount? account = await _googleSignIn.signIn();
      return account;
    } catch (error) {
      print('Google Sign-In error: $error');
      return null;
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _googleSignIn.signOut();
    } catch (error) {
      print('Google Sign-Out error: $error');
    }
  }

  // Silent sign in (for app startup)
  Future<GoogleSignInAccount?> signInSilently() async {
    try {
      return await _googleSignIn.signInSilently();
    } catch (error) {
      print('Silent sign-in error: $error');
      return null;
    }
  }

  // Get user data for backend
  Future<Map<String, dynamic>?> getUserData() async {
    final user = currentUser;
    if (user == null) return null;

    try {
      final auth = await user.authentication;
      return {
        'id': user.id,
        'email': user.email,
        'displayName': user.displayName,
        'photoUrl': user.photoUrl,
        'idToken': auth.idToken,
        'accessToken': auth.accessToken,
      };
    } catch (error) {
      print('Error getting user data: $error');
      return null;
    }
  }

  // Sync local data to cloud when user signs in
  Future<bool> syncLocalDataToCloud() async {
    try {
      final localStorage = await LocalStorageService.getInstance();
      final hasLocalData = await localStorage.hasLocalData();
      
      if (!hasLocalData) {
        return true; // Nothing to sync
      }

      final localData = await localStorage.exportGuestData();
      
      // TODO: Send local data to backend for syncing
      // For now, we'll just keep the local data
      print('Local data to sync: ${localData.keys}');
      
      return true;
    } catch (error) {
      print('Error syncing local data: $error');
      return false;
    }
  }

  // Download cloud data when user signs in
  Future<bool> downloadCloudData() async {
    try {
      // TODO: Fetch user data from backend
      // For now, we'll just return success
      print('Downloading cloud data...');
      
      return true;
    } catch (error) {
      print('Error downloading cloud data: $error');
      return false;
    }
  }

  // Handle sign-in flow with data sync
  Future<Map<String, dynamic>?> signInWithDataSync() async {
    try {
      // Sign in with Google
      final account = await signIn();
      if (account == null) {
        return null;
      }

      // Get user data
      final userData = await getUserData();
      if (userData == null) {
        await signOut();
        return null;
      }

      // Check if user has local data
      final localStorage = await LocalStorageService.getInstance();
      final hasLocalData = await localStorage.hasLocalData();

      if (hasLocalData) {
        // Ask user what to do with local data
        // For now, we'll keep local data and sync it
        await syncLocalDataToCloud();
      } else {
        // Download cloud data if available
        await downloadCloudData();
      }

      return userData;
    } catch (error) {
      print('Error in sign-in flow: $error');
      return null;
    }
  }

  // Handle sign-out with data preservation
  Future<void> signOutWithDataPreservation() async {
    try {
      // Keep local data when signing out
      await signOut();
    } catch (error) {
      print('Error in sign-out flow: $error');
    }
  }
}
