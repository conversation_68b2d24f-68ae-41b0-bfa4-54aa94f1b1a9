import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/api_models.dart';
import 'logging_service.dart';

/// API service for communicating with the backend
class ApiService {
  static const String _baseUrlKey = 'api_base_url';
  static const String _defaultBaseUrl = 'https://your-app.onrender.com/api/v1';
  
  late String _baseUrl;
  final http.Client _client;
  final LoggingService _logger;
  
  ApiService({
    http.Client? client,
    LoggingService? logger,
  }) : _client = client ?? http.Client(),
       _logger = logger ?? LoggingService();

  /// Initialize the API service
  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    _baseUrl = prefs.getString(_baseUrlKey) ?? _defaultBaseUrl;
    _logger.info('API Service initialized with base URL: $_baseUrl');
  }

  /// Update the base URL
  Future<void> updateBaseUrl(String newBaseUrl) async {
    _baseUrl = newBaseUrl;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_baseUrlKey, newBaseUrl);
    _logger.info('API base URL updated to: $_baseUrl');
  }

  /// Get current base URL
  String get baseUrl => _baseUrl;

  /// Check if the API is reachable
  Future<bool> isApiReachable() async {
    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl/quotes/health'),
        headers: _getHeaders(),
      ).timeout(const Duration(seconds: 5));
      
      return response.statusCode == 200;
    } catch (e) {
      _logger.warning('API not reachable: $e');
      return false;
    }
  }

  /// Get quotes with pagination and filtering
  Future<ApiResponse<QuotesResponse>> getQuotes({
    int page = 1,
    int limit = 20,
    List<int>? categoryIds,
    bool? featuredOnly,
    String? search,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'limit': limit.toString(),
      };
      
      if (categoryIds != null && categoryIds.isNotEmpty) {
        queryParams['category_ids'] = categoryIds.join(',');
      }
      
      if (featuredOnly == true) {
        queryParams['featured_only'] = 'true';
      }
      
      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }

      final uri = Uri.parse('$_baseUrl/quotes').replace(queryParameters: queryParams);
      final response = await _client.get(uri, headers: _getHeaders());

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return ApiResponse.success(QuotesResponse.fromJson(data));
      } else {
        return ApiResponse.error('Failed to fetch quotes: ${response.statusCode}');
      }
    } catch (e) {
      _logger.error('Error fetching quotes: $e');
      return ApiResponse.error('Network error: $e');
    }
  }

  /// Get categories
  Future<ApiResponse<List<CategoryResponse>>> getCategories() async {
    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl/categories'),
        headers: _getHeaders(),
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        final categories = data.map((json) => CategoryResponse.fromJson(json)).toList();
        return ApiResponse.success(categories);
      } else {
        return ApiResponse.error('Failed to fetch categories: ${response.statusCode}');
      }
    } catch (e) {
      _logger.error('Error fetching categories: $e');
      return ApiResponse.error('Network error: $e');
    }
  }

  /// Sync quotes for offline usage
  Future<ApiResponse<SyncResponse>> syncQuotes({
    String? lastSyncTimestamp,
    List<int>? categoryIds,
    int limit = 100,
  }) async {
    try {
      final requestBody = {
        'last_sync_timestamp': lastSyncTimestamp,
        'category_ids': categoryIds,
        'limit': limit,
      };

      final response = await _client.post(
        Uri.parse('$_baseUrl/sync/quotes'),
        headers: _getHeaders(),
        body: json.encode(requestBody),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return ApiResponse.success(SyncResponse.fromJson(data));
      } else {
        return ApiResponse.error('Failed to sync quotes: ${response.statusCode}');
      }
    } catch (e) {
      _logger.error('Error syncing quotes: $e');
      return ApiResponse.error('Network error: $e');
    }
  }

  /// Get offline quotes
  Future<ApiResponse<List<QuoteSync>>> getOfflineQuotes({
    List<int>? categoryIds,
    int count = 50,
    bool featuredOnly = false,
  }) async {
    try {
      final queryParams = <String, String>{
        'count': count.toString(),
        'featured_only': featuredOnly.toString(),
      };
      
      if (categoryIds != null && categoryIds.isNotEmpty) {
        queryParams['categories'] = categoryIds.join(',');
      }

      final uri = Uri.parse('$_baseUrl/sync/offline-quotes').replace(queryParameters: queryParams);
      final response = await _client.get(uri, headers: _getHeaders());

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        final quotes = data.map((json) => QuoteSync.fromJson(json)).toList();
        return ApiResponse.success(quotes);
      } else {
        return ApiResponse.error('Failed to fetch offline quotes: ${response.statusCode}');
      }
    } catch (e) {
      _logger.error('Error fetching offline quotes: $e');
      return ApiResponse.error('Network error: $e');
    }
  }

  /// Get widget quote
  Future<ApiResponse<WidgetQuoteResponse>> getWidgetQuote({String? category}) async {
    try {
      final queryParams = <String, String>{};
      if (category != null) {
        queryParams['category'] = category;
      }

      final uri = Uri.parse('$_baseUrl/sync/widget-quote').replace(queryParameters: queryParams);
      final response = await _client.get(uri, headers: _getHeaders());

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return ApiResponse.success(WidgetQuoteResponse.fromJson(data));
      } else {
        return ApiResponse.error('Failed to fetch widget quote: ${response.statusCode}');
      }
    } catch (e) {
      _logger.error('Error fetching widget quote: $e');
      return ApiResponse.error('Network error: $e');
    }
  }

  /// Send analytics data
  Future<ApiResponse<void>> sendAnalytics(Map<String, dynamic> analyticsData) async {
    try {
      final response = await _client.post(
        Uri.parse('$_baseUrl/sync/analytics'),
        headers: _getHeaders(),
        body: json.encode(analyticsData),
      );

      if (response.statusCode == 200) {
        return ApiResponse.success(null);
      } else {
        return ApiResponse.error('Failed to send analytics: ${response.statusCode}');
      }
    } catch (e) {
      _logger.error('Error sending analytics: $e');
      return ApiResponse.error('Network error: $e');
    }
  }

  /// Get common headers for API requests
  Map<String, String> _getHeaders() {
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': 'DailyMotivator/1.0.0',
    };
  }

  /// Dispose resources
  void dispose() {
    _client.close();
  }
}
