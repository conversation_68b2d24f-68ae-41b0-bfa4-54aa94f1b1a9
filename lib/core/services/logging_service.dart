import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../utils/platform_utils.dart';
import 'storage_service.dart';

/// Production-level logging service with different log levels and persistence
class LoggingService {
  static const String _logsKey = 'app_logs';
  static const int _maxLogEntries = 1000;
  
  static final List<LogEntry> _logs = [];
  static bool _initialized = false;
  
  /// Initialize logging service
  static Future<void> initialize() async {
    if (_initialized) return;
    
    try {
      // Load existing logs from storage
      final savedLogs = StorageService.loadData<List<dynamic>>(_logsKey, defaultValue: []);
      if (savedLogs != null) {
        _logs.addAll(
          savedLogs.map((log) => LogEntry.fromJson(log as Map<String, dynamic>))
        );
      }
      
      _initialized = true;
      
      // Log initialization
      info('LoggingService initialized with ${_logs.length} existing logs');
      
      // Set up Flutter error handling
      FlutterError.onError = (FlutterErrorDetails details) {
        error(
          'Flutter Error: ${details.exception}',
          error: details.exception,
          stackTrace: details.stack,
        );
      };
      
      // Set up platform error handling
      PlatformDispatcher.instance.onError = (error, stack) {
        LoggingService.error(
          'Platform Error: $error',
          error: error,
          stackTrace: stack,
        );
        return true;
      };
      
    } catch (e) {
      debugPrint('Failed to initialize LoggingService: $e');
    }
  }
  
  /// Log debug message
  static void debug(String message, {Map<String, dynamic>? data}) {
    _log(LogLevel.debug, message, data: data);
  }
  
  /// Log info message
  static void info(String message, {Map<String, dynamic>? data}) {
    _log(LogLevel.info, message, data: data);
  }
  
  /// Log warning message
  static void warning(String message, {Map<String, dynamic>? data}) {
    _log(LogLevel.warning, message, data: data);
  }
  
  /// Log error message
  static void error(
    String message, {
    Object? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? data,
  }) {
    _log(
      LogLevel.error,
      message,
      error: error,
      stackTrace: stackTrace,
      data: data,
    );
  }
  
  /// Log critical error
  static void critical(
    String message, {
    Object? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? data,
  }) {
    _log(
      LogLevel.critical,
      message,
      error: error,
      stackTrace: stackTrace,
      data: data,
    );
  }
  
  /// Internal logging method
  static void _log(
    LogLevel level,
    String message, {
    Object? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? data,
  }) {
    final logEntry = LogEntry(
      level: level,
      message: message,
      timestamp: DateTime.now(),
      error: error?.toString(),
      stackTrace: stackTrace?.toString(),
      data: data,
      platform: PlatformUtils.platformName,
    );
    
    _logs.add(logEntry);
    
    // Print to console in debug mode
    if (kDebugMode) {
      _printToConsole(logEntry);
    }
    
    // Maintain log size limit
    if (_logs.length > _maxLogEntries) {
      _logs.removeRange(0, _logs.length - _maxLogEntries);
    }
    
    // Save to storage periodically
    _saveLogsToStorage();
  }
  
  /// Print log entry to console with formatting
  static void _printToConsole(LogEntry entry) {
    final timestamp = entry.timestamp.toIso8601String();
    final level = entry.level.name.toUpperCase().padRight(8);
    final message = entry.message;
    
    print('[$timestamp] $level $message');
    
    if (entry.error != null) {
      print('  Error: ${entry.error}');
    }
    
    if (entry.stackTrace != null && entry.level.index >= LogLevel.error.index) {
      print('  Stack Trace:\n${entry.stackTrace}');
    }
    
    if (entry.data != null && entry.data!.isNotEmpty) {
      print('  Data: ${jsonEncode(entry.data)}');
    }
  }
  
  /// Save logs to storage
  static Future<void> _saveLogsToStorage() async {
    try {
      final logsJson = _logs.map((log) => log.toJson()).toList();
      await StorageService.saveData(_logsKey, logsJson);
    } catch (e) {
      debugPrint('Failed to save logs to storage: $e');
    }
  }
  
  /// Get all logs
  static List<LogEntry> getAllLogs() {
    return List.unmodifiable(_logs);
  }
  
  /// Get logs by level
  static List<LogEntry> getLogsByLevel(LogLevel level) {
    return _logs.where((log) => log.level == level).toList();
  }
  
  /// Get logs by date range
  static List<LogEntry> getLogsByDateRange(DateTime start, DateTime end) {
    return _logs.where((log) => 
      log.timestamp.isAfter(start) && log.timestamp.isBefore(end)
    ).toList();
  }
  
  /// Get recent logs
  static List<LogEntry> getRecentLogs({int count = 100}) {
    final startIndex = (_logs.length - count).clamp(0, _logs.length);
    return _logs.sublist(startIndex);
  }
  
  /// Clear all logs
  static Future<void> clearLogs() async {
    _logs.clear();
    await StorageService.saveData(_logsKey, []);
    info('All logs cleared');
  }
  
  /// Export logs as JSON string
  static String exportLogs() {
    final logsData = {
      'exportTimestamp': DateTime.now().toIso8601String(),
      'platform': PlatformUtils.platformName,
      'totalLogs': _logs.length,
      'logs': _logs.map((log) => log.toJson()).toList(),
    };
    
    return jsonEncode(logsData);
  }
  
  /// Get logging statistics
  static Map<String, dynamic> getLoggingStats() {
    final levelCounts = <String, int>{};
    
    for (final level in LogLevel.values) {
      levelCounts[level.name] = _logs.where((log) => log.level == level).length;
    }
    
    return {
      'totalLogs': _logs.length,
      'levelCounts': levelCounts,
      'oldestLog': _logs.isNotEmpty ? _logs.first.timestamp.toIso8601String() : null,
      'newestLog': _logs.isNotEmpty ? _logs.last.timestamp.toIso8601String() : null,
      'platform': PlatformUtils.platformName,
    };
  }
  
  /// Log user action for analytics
  static void logUserAction(String action, {Map<String, dynamic>? data}) {
    info('User Action: $action', data: {
      'action': action,
      'timestamp': DateTime.now().toIso8601String(),
      ...?data,
    });
  }
  
  /// Log performance metric
  static void logPerformance(String metric, Duration duration, {Map<String, dynamic>? data}) {
    info('Performance: $metric took ${duration.inMilliseconds}ms', data: {
      'metric': metric,
      'duration_ms': duration.inMilliseconds,
      'timestamp': DateTime.now().toIso8601String(),
      ...?data,
    });
  }
  
  /// Log app lifecycle event
  static void logAppLifecycle(String event, {Map<String, dynamic>? data}) {
    info('App Lifecycle: $event', data: {
      'event': event,
      'timestamp': DateTime.now().toIso8601String(),
      ...?data,
    });
  }
}

/// Log levels in order of severity
enum LogLevel {
  debug,
  info,
  warning,
  error,
  critical,
}

/// Log entry model
class LogEntry {
  final LogLevel level;
  final String message;
  final DateTime timestamp;
  final String? error;
  final String? stackTrace;
  final Map<String, dynamic>? data;
  final String platform;
  
  LogEntry({
    required this.level,
    required this.message,
    required this.timestamp,
    this.error,
    this.stackTrace,
    this.data,
    required this.platform,
  });
  
  Map<String, dynamic> toJson() {
    return {
      'level': level.name,
      'message': message,
      'timestamp': timestamp.toIso8601String(),
      'error': error,
      'stackTrace': stackTrace,
      'data': data,
      'platform': platform,
    };
  }
  
  factory LogEntry.fromJson(Map<String, dynamic> json) {
    return LogEntry(
      level: LogLevel.values.firstWhere(
        (l) => l.name == json['level'],
        orElse: () => LogLevel.info,
      ),
      message: json['message'] ?? '',
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
      error: json['error'],
      stackTrace: json['stackTrace'],
      data: json['data'] as Map<String, dynamic>?,
      platform: json['platform'] ?? 'unknown',
    );
  }
  
  @override
  String toString() {
    return '${timestamp.toIso8601String()} [${level.name.toUpperCase()}] $message';
  }
}
