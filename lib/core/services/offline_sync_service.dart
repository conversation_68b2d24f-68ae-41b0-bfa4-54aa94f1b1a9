import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/api_models.dart';
import '../../domain/entities/quote.dart';
import '../../domain/entities/category.dart';
import 'api_service.dart';
import 'logging_service.dart';

/// Service for managing offline data synchronization
class OfflineSyncService {
  static const String _lastSyncKey = 'last_sync_timestamp';
  static const String _offlineQuotesKey = 'offline_quotes';
  static const String _offlineCategoriesKey = 'offline_categories';
  
  final ApiService _apiService;
  
  OfflineSyncService({
    required ApiService apiService,
  }) : _apiService = apiService;

  /// Check if device is online and API is reachable
  Future<bool> isOnline() async {
    return await _apiService.isApiReachable();
  }

  /// Get last sync timestamp
  Future<String?> getLastSyncTimestamp() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_lastSyncKey);
  }

  /// Update last sync timestamp
  Future<void> updateLastSyncTimestamp(String timestamp) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_lastSyncKey, timestamp);
  }

  /// Sync quotes from API to local storage
  Future<bool> syncQuotes({
    List<int>? categoryIds,
    int limit = 100,
    bool forceFullSync = false,
  }) async {
    try {
      LoggingService.info('Starting quote sync...');

      if (!await isOnline()) {
        LoggingService.warning('Device is offline, skipping sync');
        return false;
      }

      String? lastSync = forceFullSync ? null : await getLastSyncTimestamp();

      final response = await _apiService.syncQuotes(
        lastSyncTimestamp: lastSync,
        categoryIds: categoryIds,
        limit: limit,
      );

      if (response.isSuccess && response.data != null) {
        final syncData = response.data!;

        // Store quotes locally
        await _storeOfflineQuotes(syncData.quotes);

        // Update sync timestamp
        await updateLastSyncTimestamp(syncData.syncTimestamp);

        LoggingService.info('Synced ${syncData.quotes.length} quotes');
        return true;
      } else {
        LoggingService.error('Sync failed: ${response.error}');
        return false;
      }
    } catch (e) {
      LoggingService.error('Error during sync: $e');
      return false;
    }
  }

  /// Get offline quotes for initial app load
  Future<bool> loadOfflineQuotes({
    List<int>? categoryIds,
    int count = 50,
    bool featuredOnly = false,
  }) async {
    try {
      LoggingService.info('Loading offline quotes...');

      if (!await isOnline()) {
        LoggingService.warning('Device is offline, using cached quotes only');
        return false;
      }

      final response = await _apiService.getOfflineQuotes(
        categoryIds: categoryIds,
        count: count,
        featuredOnly: featuredOnly,
      );

      if (response.isSuccess && response.data != null) {
        await _storeOfflineQuotes(response.data!);
        LoggingService.info('Loaded ${response.data!.length} offline quotes');
        return true;
      } else {
        LoggingService.error('Failed to load offline quotes: ${response.error}');
        return false;
      }
    } catch (e) {
      LoggingService.error('Error loading offline quotes: $e');
      return false;
    }
  }

  /// Get stored offline quotes
  Future<List<Quote>> getStoredQuotes({List<int>? categoryIds}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final quotesJson = prefs.getString(_offlineQuotesKey);
      
      if (quotesJson == null) {
        return [];
      }

      final List<dynamic> quotesData = json.decode(quotesJson);
      final quotes = quotesData
          .map((json) => QuoteSync.fromJson(json))
          .where((quoteSync) {
            if (categoryIds == null || categoryIds.isEmpty) return true;
            return categoryIds.contains(quoteSync.categoryId);
          })
          .map((quoteSync) => Quote(
            id: quoteSync.id,
            text: quoteSync.text,
            author: quoteSync.author,
            categoryId: quoteSync.categoryId,
            category: quoteSync.categoryName,
            tags: quoteSync.tags,
            isFeatured: quoteSync.isFeatured,
            qualityScore: quoteSync.qualityScore,
            length: quoteSync.text.length,
            wordCount: quoteSync.text.split(' ').length,
            readingTime: (quoteSync.text.split(' ').length / 200 * 60).round(),
            createdAt: DateTime.parse(quoteSync.createdAt),
            updatedAt: DateTime.parse(quoteSync.updatedAt),
          ))
          .toList();

      return quotes;
    } catch (e) {
      LoggingService.error('Error getting stored quotes: $e');
      return [];
    }
  }

  /// Store quotes locally
  Future<void> _storeOfflineQuotes(List<QuoteSync> quotes) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Get existing quotes
      final existingJson = prefs.getString(_offlineQuotesKey);
      List<QuoteSync> existingQuotes = [];

      if (existingJson != null) {
        final List<dynamic> existingData = json.decode(existingJson);
        existingQuotes = existingData.map((json) => QuoteSync.fromJson(json)).toList();
      }

      // Merge new quotes with existing ones (avoid duplicates)
      final Map<int, QuoteSync> quotesMap = {
        for (var quote in existingQuotes) quote.id: quote
      };

      for (var quote in quotes) {
        quotesMap[quote.id] = quote;
      }

      // Store updated quotes
      final updatedQuotes = quotesMap.values.toList();
      final quotesJson = json.encode(updatedQuotes.map((q) => q.toJson()).toList());
      await prefs.setString(_offlineQuotesKey, quotesJson);

      LoggingService.info('Stored ${updatedQuotes.length} quotes locally');
    } catch (e) {
      LoggingService.error('Error storing offline quotes: $e');
    }
  }

  /// Sync categories
  Future<bool> syncCategories() async {
    try {
      if (!await isOnline()) {
        return false;
      }

      final response = await _apiService.getCategories();
      
      if (response.isSuccess && response.data != null) {
        await _storeOfflineCategories(response.data!);
        return true;
      }
      
      return false;
    } catch (e) {
      LoggingService.error('Error syncing categories: $e');
      return false;
    }
  }

  /// Store categories locally
  Future<void> _storeOfflineCategories(List<CategoryResponse> categories) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final categoriesJson = json.encode(categories.map((c) => {
        'id': c.id,
        'name': c.name,
        'slug': c.slug,
        'description': c.description,
        'color': c.color,
        'icon': c.icon,
        'quote_count': c.quoteCount,
        'sort_order': c.sortOrder,
      }).toList());

      await prefs.setString(_offlineCategoriesKey, categoriesJson);
      LoggingService.info('Stored ${categories.length} categories locally');
    } catch (e) {
      LoggingService.error('Error storing categories: $e');
    }
  }

  /// Get stored categories
  Future<List<Category>> getStoredCategories() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final categoriesJson = prefs.getString(_offlineCategoriesKey);
      
      if (categoriesJson == null) {
        return [];
      }

      final List<dynamic> categoriesData = json.decode(categoriesJson);
      final categories = categoriesData.map((json) => Category(
        id: json['id'] as int,
        name: json['name'] as String,
        slug: json['slug'] as String,
        description: json['description'] as String? ?? '',
        color: json['color'] as String? ?? '#4ECDC4',
        icon: json['icon'] as String? ?? '📝',
        quoteCount: json['quote_count'] as int? ?? 0,
        sortOrder: json['sort_order'] as int? ?? 0,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      )).toList();

      return categories;
    } catch (e) {
      LoggingService.error('Error getting stored categories: $e');
      return [];
    }
  }

  /// Perform full sync (quotes + categories)
  Future<bool> performFullSync({
    List<int>? categoryIds,
    bool forceSync = false,
  }) async {
    try {
      LoggingService.info('Starting full sync...');

      if (!await isOnline()) {
        LoggingService.warning('Device is offline, cannot perform full sync');
        return false;
      }

      // Sync categories first
      final categoriesSuccess = await syncCategories();

      // Sync quotes
      final quotesSuccess = await syncQuotes(
        categoryIds: categoryIds,
        forceFullSync: forceSync,
      );

      final success = categoriesSuccess && quotesSuccess;
      LoggingService.info('Full sync ${success ? 'completed' : 'failed'}');

      return success;
    } catch (e) {
      LoggingService.error('Error during full sync: $e');
      return false;
    }
  }

  /// Clear all offline data
  Future<void> clearOfflineData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_offlineQuotesKey);
      await prefs.remove(_offlineCategoriesKey);
      await prefs.remove(_lastSyncKey);
      LoggingService.info('Cleared all offline data');
    } catch (e) {
      LoggingService.error('Error clearing offline data: $e');
    }
  }

  /// Get sync statistics
  Future<Map<String, dynamic>> getSyncStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastSync = prefs.getString(_lastSyncKey);
      final quotesJson = prefs.getString(_offlineQuotesKey);
      final categoriesJson = prefs.getString(_offlineCategoriesKey);

      int quotesCount = 0;
      int categoriesCount = 0;

      if (quotesJson != null) {
        final List<dynamic> quotes = json.decode(quotesJson);
        quotesCount = quotes.length;
      }

      if (categoriesJson != null) {
        final List<dynamic> categories = json.decode(categoriesJson);
        categoriesCount = categories.length;
      }

      return {
        'last_sync': lastSync,
        'quotes_count': quotesCount,
        'categories_count': categoriesCount,
        'is_online': await isOnline(),
      };
    } catch (e) {
      LoggingService.error('Error getting sync stats: $e');
      return {
        'last_sync': null,
        'quotes_count': 0,
        'categories_count': 0,
        'is_online': false,
      };
    }
  }
}
