import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class TimeUtils {
  static String formatTimeOfDay(TimeOfDay time) {
    final now = DateTime.now();
    final dateTime = DateTime(now.year, now.month, now.day, time.hour, time.minute);
    return DateFormat.jm().format(dateTime);
  }

  static String formatTimeOfDayWithSeconds(TimeOfDay time) {
    final now = DateTime.now();
    final dateTime = DateTime(now.year, now.month, now.day, time.hour, time.minute);
    return DateFormat('h:mm:ss a').format(dateTime);
  }

  static String formatTime24Hour(TimeOfDay time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  static TimeOfDay parseTimeString(String timeString) {
    try {
      final parts = timeString.split(':');
      if (parts.length >= 2) {
        final hour = int.tryParse(parts[0]) ?? 0;
        final minute = int.tryParse(parts[1]) ?? 0;
        return TimeOfDay(hour: hour, minute: minute);
      }
    } catch (e) {
      debugPrint('Error parsing time string: $e');
    }
    return const TimeOfDay(hour: 9, minute: 0);
  }

  static List<TimeOfDay> generateDefaultNotificationTimes(int count) {
    final times = <TimeOfDay>[];

    switch (count) {
      case 1:
        times.add(const TimeOfDay(hour: 9, minute: 0));
        break;
      case 2:
        times.addAll([
          const TimeOfDay(hour: 9, minute: 0),
          const TimeOfDay(hour: 18, minute: 0),
        ]);
        break;
      case 3:
        times.addAll([
          const TimeOfDay(hour: 9, minute: 0),
          const TimeOfDay(hour: 14, minute: 0),
          const TimeOfDay(hour: 18, minute: 0),
        ]);
        break;
      case 4:
        times.addAll([
          const TimeOfDay(hour: 8, minute: 0),
          const TimeOfDay(hour: 12, minute: 0),
          const TimeOfDay(hour: 16, minute: 0),
          const TimeOfDay(hour: 20, minute: 0),
        ]);
        break;
      case 5:
        times.addAll([
          const TimeOfDay(hour: 8, minute: 0),
          const TimeOfDay(hour: 11, minute: 0),
          const TimeOfDay(hour: 14, minute: 0),
          const TimeOfDay(hour: 17, minute: 0),
          const TimeOfDay(hour: 20, minute: 0),
        ]);
        break;
      default:
        // For counts > 5, distribute evenly throughout the day
        const startHour = 8;
        const endHour = 20;
        final interval = (endHour - startHour) / (count - 1);

        for (int i = 0; i < count; i++) {
          final hour = (startHour + (interval * i)).round();
          times.add(TimeOfDay(hour: hour, minute: 0));
        }
    }

    return times;
  }

  static String formatTimeList(List<TimeOfDay> times) {
    if (times.isEmpty) return 'No times set';
    if (times.length == 1) return formatTimeOfDay(times.first);

    final formattedTimes = times.map(formatTimeOfDay).toList();
    if (formattedTimes.length == 2) {
      return '${formattedTimes[0]} and ${formattedTimes[1]}';
    }

    final lastTime = formattedTimes.removeLast();
    return '${formattedTimes.join(', ')}, and $lastTime';
  }

  static bool isTimeInRange(TimeOfDay time, TimeOfDay start, TimeOfDay end) {
    final timeMinutes = time.hour * 60 + time.minute;
    final startMinutes = start.hour * 60 + start.minute;
    final endMinutes = end.hour * 60 + end.minute;

    if (startMinutes <= endMinutes) {
      return timeMinutes >= startMinutes && timeMinutes <= endMinutes;
    } else {
      // Handle overnight range (e.g., 22:00 to 06:00)
      return timeMinutes >= startMinutes || timeMinutes <= endMinutes;
    }
  }

  static Duration timeDifference(TimeOfDay start, TimeOfDay end) {
    final startMinutes = start.hour * 60 + start.minute;
    final endMinutes = end.hour * 60 + end.minute;

    int diffMinutes;
    if (endMinutes >= startMinutes) {
      diffMinutes = endMinutes - startMinutes;
    } else {
      // Handle overnight difference
      diffMinutes = (24 * 60) - startMinutes + endMinutes;
    }

    return Duration(minutes: diffMinutes);
  }

  static TimeOfDay addMinutes(TimeOfDay time, int minutes) {
    final totalMinutes = time.hour * 60 + time.minute + minutes;
    final hours = (totalMinutes ~/ 60) % 24;
    final mins = totalMinutes % 60;
    return TimeOfDay(hour: hours, minute: mins);
  }

  static String getTimeOfDayGreeting() {
    final now = DateTime.now();
    final hour = now.hour;

    if (hour < 6) {
      return 'Good night';
    } else if (hour < 12) {
      return 'Good morning';
    } else if (hour < 17) {
      return 'Good afternoon';
    } else if (hour < 21) {
      return 'Good evening';
    } else {
      return 'Good night';
    }
  }

  static String getMotivationalTimeMessage() {
    final now = DateTime.now();
    final hour = now.hour;

    if (hour < 6) {
      return 'Even in the quiet hours, inspiration awaits';
    } else if (hour < 9) {
      return 'Start your day with purpose';
    } else if (hour < 12) {
      return 'Keep the momentum going';
    } else if (hour < 15) {
      return 'Afternoon motivation boost';
    } else if (hour < 18) {
      return 'Push through to the finish';
    } else if (hour < 21) {
      return 'Reflect on today\'s achievements';
    } else {
      return 'Rest well, tomorrow brings new opportunities';
    }
  }

  static List<TimeOfDay> generateRandomTimes(int count) {
    final random = DateTime.now().millisecondsSinceEpoch;
    final times = <TimeOfDay>[];

    // Generate random times between 6 AM and 10 PM
    const minHour = 6;
    const maxHour = 22;

    for (int i = 0; i < count; i++) {
      final hour = minHour + ((random + i * 37) % (maxHour - minHour));
      final minute = ((random + i * 23) % 60);
      times.add(TimeOfDay(hour: hour, minute: minute));
    }

    // Sort times by hour and minute
    times.sort((a, b) {
      if (a.hour != b.hour) return a.hour.compareTo(b.hour);
      return a.minute.compareTo(b.minute);
    });

    return times;
  }

  static List<String> timeOfDayListToStringList(List<TimeOfDay> times) {
    return times.map((time) => '${time.hour}:${time.minute}').toList();
  }

  static List<TimeOfDay> stringListToTimeOfDayList(List<String> timeStrings) {
    return timeStrings.map((timeString) => parseTimeString(timeString)).toList();
  }
}
