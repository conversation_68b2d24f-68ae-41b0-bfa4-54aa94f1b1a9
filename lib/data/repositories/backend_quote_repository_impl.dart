import 'package:dartz/dartz.dart';
import '../../core/errors/exceptions.dart';
import '../../core/errors/failures.dart';
import '../../domain/entities/quote.dart';
import '../../domain/entities/category.dart';
import '../../domain/repositories/quote_repository.dart';
import '../datasources/backend_quote_datasource.dart';

class BackendQuoteRepositoryImpl implements QuoteRepository {
  final BackendQuoteDataSource backendDataSource;

  BackendQuoteRepositoryImpl({required this.backendDataSource});

  @override
  Future<Either<Failure, Quote>> getRandomQuote(List<String> categories) async {
    try {
      // Convert category names to IDs if needed
      // For now, we'll use the first category or null
      List<int>? categoryIds;
      if (categories.isNotEmpty) {
        // This is a simplified approach - in a real app you'd map category names to IDs
        categoryIds = [1]; // Default to first category
      }
      
      final quote = await backendDataSource.getRandomQuote(categoryIds: categoryIds);
      
      // Record view interaction
      await backendDataSource.recordQuoteInteraction(quote.id, 'view');
      
      return Right(quote);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Quote>>> getQuotesByCategory(String category) async {
    try {
      // Convert category name to ID - simplified approach
      int? categoryId;
      if (category.isNotEmpty && category != 'all') {
        categoryId = 1; // Default mapping
      }
      
      final quotes = await backendDataSource.getQuotes(
        categoryId: categoryId,
        pageSize: 50,
      );
      return Right(quotes);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<String>>> getAvailableCategories() async {
    try {
      final categories = await backendDataSource.getActiveCategories();
      final categoryNames = categories.map((cat) => cat.name).toList();
      return Right(categoryNames);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Quote>>> searchQuotes(String query) async {
    try {
      final quotes = await backendDataSource.getQuotes(
        search: query,
        pageSize: 50,
      );
      return Right(quotes);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Quote>>> getFavoriteQuotes() async {
    try {
      // This would fetch user's favorite quotes from backend
      // For now, return empty list as this requires authentication
      return const Right([]);
    } catch (e) {
      return Left(CacheFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> addToFavorites(String quoteId) async {
    try {
      final id = int.tryParse(quoteId);
      if (id != null) {
        await backendDataSource.recordQuoteInteraction(id, 'favorite');
      }
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: e.toString(), code: 500));
    }
  }

  @override
  Future<Either<Failure, void>> removeFromFavorites(String quoteId) async {
    try {
      final id = int.tryParse(quoteId);
      if (id != null) {
        await backendDataSource.recordQuoteInteraction(id, 'unfavorite');
      }
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: e.toString(), code: 500));
    }
  }

  @override
  Future<Either<Failure, Quote>> getQuoteById(String id) async {
    try {
      final quoteId = int.tryParse(id);
      if (quoteId == null) {
        return Left(const ValidationFailure(message: 'Invalid quote ID'));
      }
      
      final quote = await backendDataSource.getQuoteById(quoteId);
      return Right(quote);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> cacheQuotes(List<Quote> quotes) async {
    try {
      // This would typically save to local storage
      // For now, just return success
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Quote>>> getCachedQuotes() async {
    try {
      // This would typically fetch from local storage
      // For now, return empty list
      return const Right([]);
    } catch (e) {
      return Left(CacheFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> clearCache() async {
    try {
      // This would typically clear local storage
      // For now, just return success
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure(message: e.toString()));
    }
  }

  // Additional methods for backend integration
  Future<Either<Failure, List<Category>>> getCategories() async {
    try {
      final categories = await backendDataSource.getCategories();
      return Right(categories);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  Future<Either<Failure, void>> likeQuote(int quoteId) async {
    try {
      await backendDataSource.recordQuoteInteraction(quoteId, 'like');
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: e.toString(), code: 500));
    }
  }

  Future<Either<Failure, void>> shareQuote(int quoteId) async {
    try {
      await backendDataSource.recordQuoteInteraction(quoteId, 'share');
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: e.toString(), code: 500));
    }
  }
}
