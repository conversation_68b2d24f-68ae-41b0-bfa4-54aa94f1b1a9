import '../../domain/entities/category.dart';

class CategoryModel extends Category {
  const CategoryModel({
    required super.id,
    required super.name,
    required super.slug,
    required super.description,
    required super.color,
    required super.icon,
    super.imageUrl,
    super.isActive = true,
    super.sortOrder = 0,
    super.quoteCount = 0,
    super.popularityScore = 0,
    required super.createdAt,
    required super.updatedAt,
  });

  factory CategoryModel.fromJson(Map<String, dynamic> json) {
    return CategoryModel(
      id: json['id'] as int,
      name: json['name'] as String,
      slug: json['slug'] as String,
      description: json['description'] as String,
      color: json['color'] as String,
      icon: json['icon'] as String,
      imageUrl: json['image_url'] as String?,
      isActive: json['is_active'] as bool? ?? true,
      sortOrder: json['sort_order'] as int? ?? 0,
      quoteCount: json['quote_count'] as int? ?? 0,
      popularityScore: json['popularity_score'] as int? ?? 0,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'slug': slug,
      'description': description,
      'color': color,
      'icon': icon,
      'image_url': imageUrl,
      'is_active': isActive,
      'sort_order': sortOrder,
      'quote_count': quoteCount,
      'popularity_score': popularityScore,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  factory CategoryModel.fromEntity(Category category) {
    return CategoryModel(
      id: category.id,
      name: category.name,
      slug: category.slug,
      description: category.description,
      color: category.color,
      icon: category.icon,
      imageUrl: category.imageUrl,
      isActive: category.isActive,
      sortOrder: category.sortOrder,
      quoteCount: category.quoteCount,
      popularityScore: category.popularityScore,
      createdAt: category.createdAt,
      updatedAt: category.updatedAt,
    );
  }

  @override
  CategoryModel copyWith({
    int? id,
    String? name,
    String? slug,
    String? description,
    String? color,
    String? icon,
    String? imageUrl,
    bool? isActive,
    int? sortOrder,
    int? quoteCount,
    int? popularityScore,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CategoryModel(
      id: id ?? this.id,
      name: name ?? this.name,
      slug: slug ?? this.slug,
      description: description ?? this.description,
      color: color ?? this.color,
      icon: icon ?? this.icon,
      imageUrl: imageUrl ?? this.imageUrl,
      isActive: isActive ?? this.isActive,
      sortOrder: sortOrder ?? this.sortOrder,
      quoteCount: quoteCount ?? this.quoteCount,
      popularityScore: popularityScore ?? this.popularityScore,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
