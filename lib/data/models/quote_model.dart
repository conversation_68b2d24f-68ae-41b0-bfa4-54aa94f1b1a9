import '../../domain/entities/quote.dart';

class QuoteModel extends Quote {
  const QuoteModel({
    required int id,
    required String text,
    required String author,
    String? source,
    required int categoryId,
    required List<String> tags,
    String language = 'en',
    required int length,
    required int wordCount,
    required int readingTime,
    bool isApproved = true,
    bool isFavorite = false,
    bool isFeatured = false,
    double qualityScore = 0.0,
    String? sourceUrl,
    bool attributionRequired = false,
    String? copyrightInfo,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? lastShown,
  }) : super(
          id: id,
          text: text,
          author: author,
          source: source,
          categoryId: categoryId,
          tags: tags,
          language: language,
          length: length,
          wordCount: wordCount,
          readingTime: readingTime,
          isApproved: isApproved,
          isFavorite: isFavorite,
          isFeatured: isFeatured,
          qualityScore: qualityScore,
          sourceUrl: sourceUrl,
          attributionRequired: attributionRequired,
          copyrightInfo: copyrightInfo,
          createdAt: createdAt,
          updatedAt: updatedAt,
          lastShown: lastShown,
        );

  factory QuoteModel.fromJson(Map<String, dynamic> json) {
    return QuoteModel(
      id: json['id'] as int,
      text: json['text'] as String,
      author: json['author'] as String,
      source: json['source'] as String?,
      categoryId: json['category_id'] as int,
      tags: json['tags'] != null
          ? List<String>.from(json['tags'])
          : [],
      language: json['language'] as String? ?? 'en',
      length: json['length'] as int,
      wordCount: json['word_count'] as int,
      readingTime: json['reading_time'] as int,
      isApproved: json['is_approved'] as bool? ?? true,
      isFavorite: json['is_favorite'] as bool? ?? false,
      isFeatured: json['is_featured'] as bool? ?? false,
      qualityScore: (json['quality_score'] as num?)?.toDouble() ?? 0.0,
      sourceUrl: json['source_url'] as String?,
      attributionRequired: json['attribution_required'] as bool? ?? false,
      copyrightInfo: json['copyright_info'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      lastShown: json['last_shown'] != null
          ? DateTime.parse(json['last_shown'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'author': author,
      'source': source,
      'category_id': categoryId,
      'tags': tags,
      'language': language,
      'length': length,
      'word_count': wordCount,
      'reading_time': readingTime,
      'is_approved': isApproved,
      'is_favorite': isFavorite,
      'is_featured': isFeatured,
      'quality_score': qualityScore,
      'source_url': sourceUrl,
      'attribution_required': attributionRequired,
      'copyright_info': copyrightInfo,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'last_shown': lastShown?.toIso8601String(),
    };
  }

  factory QuoteModel.fromEntity(Quote quote) {
    return QuoteModel(
      id: quote.id,
      text: quote.text,
      author: quote.author,
      source: quote.source,
      categoryId: quote.categoryId,
      tags: quote.tags,
      language: quote.language,
      length: quote.length,
      wordCount: quote.wordCount,
      readingTime: quote.readingTime,
      isApproved: quote.isApproved,
      isFavorite: quote.isFavorite,
      isFeatured: quote.isFeatured,
      qualityScore: quote.qualityScore,
      sourceUrl: quote.sourceUrl,
      attributionRequired: quote.attributionRequired,
      copyrightInfo: quote.copyrightInfo,
      createdAt: quote.createdAt,
      updatedAt: quote.updatedAt,
      lastShown: quote.lastShown,
    );
  }

  @override
  QuoteModel copyWith({
    int? id,
    String? text,
    String? author,
    String? source,
    int? categoryId,
    List<String>? tags,
    String? language,
    int? length,
    int? wordCount,
    int? readingTime,
    bool? isApproved,
    bool? isFavorite,
    bool? isFeatured,
    double? qualityScore,
    String? sourceUrl,
    bool? attributionRequired,
    String? copyrightInfo,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastShown,
  }) {
    return QuoteModel(
      id: id ?? this.id,
      text: text ?? this.text,
      author: author ?? this.author,
      source: source ?? this.source,
      categoryId: categoryId ?? this.categoryId,
      tags: tags ?? this.tags,
      language: language ?? this.language,
      length: length ?? this.length,
      wordCount: wordCount ?? this.wordCount,
      readingTime: readingTime ?? this.readingTime,
      isApproved: isApproved ?? this.isApproved,
      isFavorite: isFavorite ?? this.isFavorite,
      isFeatured: isFeatured ?? this.isFeatured,
      qualityScore: qualityScore ?? this.qualityScore,
      sourceUrl: sourceUrl ?? this.sourceUrl,
      attributionRequired: attributionRequired ?? this.attributionRequired,
      copyrightInfo: copyrightInfo ?? this.copyrightInfo,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastShown: lastShown ?? this.lastShown,
    );
  }

  // Factory for creating a simple quote (for fallback/testing)
  factory QuoteModel.create({
    required String text,
    required String author,
    required String category,
    List<String> tags = const [],
  }) {
    final now = DateTime.now();
    return QuoteModel(
      id: now.millisecondsSinceEpoch,
      text: text,
      author: author,
      categoryId: 1, // Default category ID
      tags: tags,
      length: text.length,
      wordCount: text.split(' ').length,
      readingTime: (text.split(' ').length / 200 * 60).ceil(), // ~200 words per minute
      createdAt: now,
      updatedAt: now,
    );
  }
}
