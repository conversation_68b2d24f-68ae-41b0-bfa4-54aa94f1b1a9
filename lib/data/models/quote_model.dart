import '../../domain/entities/quote.dart';

class QuoteModel extends Quote {
  const QuoteModel({
    required super.id,
    required super.text,
    required super.author,
    super.source,
    required super.categoryId,
    required super.category,
    required super.tags,
    super.language = 'en',
    required super.length,
    required super.wordCount,
    required super.readingTime,
    super.isApproved = true,
    super.isFavorite = false,
    super.isFeatured = false,
    super.qualityScore = 0.0,
    super.sourceUrl,
    super.attributionRequired = false,
    super.copyrightInfo,
    required super.createdAt,
    required super.updatedAt,
    super.lastShown,
  });

  factory QuoteModel.fromJson(Map<String, dynamic> json) {
    return QuoteModel(
      id: json['id'] as int,
      text: json['text'] as String,
      author: json['author'] as String,
      source: json['source'] as String?,
      categoryId: json['category_id'] as int,
      category: json['category'] as String? ?? 'general',
      tags: json['tags'] != null
          ? List<String>.from(json['tags'])
          : [],
      language: json['language'] as String? ?? 'en',
      length: json['length'] as int,
      wordCount: json['word_count'] as int,
      readingTime: json['reading_time'] as int,
      isApproved: json['is_approved'] as bool? ?? true,
      isFavorite: json['is_favorite'] as bool? ?? false,
      isFeatured: json['is_featured'] as bool? ?? false,
      qualityScore: (json['quality_score'] as num?)?.toDouble() ?? 0.0,
      sourceUrl: json['source_url'] as String?,
      attributionRequired: json['attribution_required'] as bool? ?? false,
      copyrightInfo: json['copyright_info'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      lastShown: json['last_shown'] != null
          ? DateTime.parse(json['last_shown'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'author': author,
      'source': source,
      'category_id': categoryId,
      'category': category,
      'tags': tags,
      'language': language,
      'length': length,
      'word_count': wordCount,
      'reading_time': readingTime,
      'is_approved': isApproved,
      'is_favorite': isFavorite,
      'is_featured': isFeatured,
      'quality_score': qualityScore,
      'source_url': sourceUrl,
      'attribution_required': attributionRequired,
      'copyright_info': copyrightInfo,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'last_shown': lastShown?.toIso8601String(),
    };
  }

  factory QuoteModel.fromEntity(Quote quote) {
    return QuoteModel(
      id: quote.id,
      text: quote.text,
      author: quote.author,
      source: quote.source,
      categoryId: quote.categoryId,
      category: quote.category,
      tags: quote.tags,
      language: quote.language,
      length: quote.length,
      wordCount: quote.wordCount,
      readingTime: quote.readingTime,
      isApproved: quote.isApproved,
      isFavorite: quote.isFavorite,
      isFeatured: quote.isFeatured,
      qualityScore: quote.qualityScore,
      sourceUrl: quote.sourceUrl,
      attributionRequired: quote.attributionRequired,
      copyrightInfo: quote.copyrightInfo,
      createdAt: quote.createdAt,
      updatedAt: quote.updatedAt,
      lastShown: quote.lastShown,
    );
  }

  @override
  QuoteModel copyWith({
    int? id,
    String? text,
    String? author,
    String? source,
    int? categoryId,
    String? category,
    List<String>? tags,
    String? language,
    int? length,
    int? wordCount,
    int? readingTime,
    bool? isApproved,
    bool? isFavorite,
    bool? isFeatured,
    double? qualityScore,
    String? sourceUrl,
    bool? attributionRequired,
    String? copyrightInfo,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastShown,
  }) {
    return QuoteModel(
      id: id ?? this.id,
      text: text ?? this.text,
      author: author ?? this.author,
      source: source ?? this.source,
      categoryId: categoryId ?? this.categoryId,
      category: category ?? this.category,
      tags: tags ?? this.tags,
      language: language ?? this.language,
      length: length ?? this.length,
      wordCount: wordCount ?? this.wordCount,
      readingTime: readingTime ?? this.readingTime,
      isApproved: isApproved ?? this.isApproved,
      isFavorite: isFavorite ?? this.isFavorite,
      isFeatured: isFeatured ?? this.isFeatured,
      qualityScore: qualityScore ?? this.qualityScore,
      sourceUrl: sourceUrl ?? this.sourceUrl,
      attributionRequired: attributionRequired ?? this.attributionRequired,
      copyrightInfo: copyrightInfo ?? this.copyrightInfo,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastShown: lastShown ?? this.lastShown,
    );
  }

  // Factory for creating a simple quote (for fallback/testing)
  factory QuoteModel.create({
    required String text,
    required String author,
    required String category,
    List<String> tags = const [],
  }) {
    final now = DateTime.now();
    return QuoteModel(
      id: now.millisecondsSinceEpoch,
      text: text,
      author: author,
      categoryId: 1, // Default category ID
      category: category,
      tags: tags,
      length: text.length,
      wordCount: text.split(' ').length,
      readingTime: (text.split(' ').length / 200 * 60).ceil(), // ~200 words per minute
      createdAt: now,
      updatedAt: now,
    );
  }
}
