import '../models/quote_model.dart';
import '../models/category_model.dart';
import '../../core/network/api_client.dart';
import '../../core/errors/exceptions.dart';

abstract class BackendQuoteDataSource {
  Future<QuoteModel> getRandomQuote({List<int>? categoryIds});
  Future<List<QuoteModel>> getQuotes({
    int page = 1,
    int pageSize = 20,
    String? search,
    int? categoryId,
    String? author,
    String? sortBy,
    String? sortOrder,
  });
  Future<QuoteModel> getQuoteById(int id);
  Future<List<CategoryModel>> getCategories();
  Future<List<CategoryModel>> getActiveCategories();
  Future<void> recordQuoteInteraction(int quoteId, String interactionType);
}

class BackendQuoteDataSourceImpl implements BackendQuoteDataSource {
  final ApiClient apiClient;

  BackendQuoteDataSourceImpl({required this.apiClient});

  @override
  Future<QuoteModel> getRandomQuote({List<int>? categoryIds}) async {
    try {
      final queryParams = <String, String>{};
      if (categoryIds != null && categoryIds.isNotEmpty) {
        queryParams['category_id'] = categoryIds.first.toString();
      }

      final response = await apiClient.get(
        '/quotes/random',
        queryParameters: queryParams,
      );

      return QuoteModel.fromJson(response);
    } catch (e) {
      throw ServerException(
        message: 'Failed to fetch random quote: ${e.toString()}',
        code: 500,
      );
    }
  }

  @override
  Future<List<QuoteModel>> getQuotes({
    int page = 1,
    int pageSize = 20,
    String? search,
    int? categoryId,
    String? author,
    String? sortBy,
    String? sortOrder,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'page_size': pageSize.toString(),
      };

      if (search != null && search.isNotEmpty) {
        queryParams['query'] = search;
      }
      if (categoryId != null) {
        queryParams['category_id'] = categoryId.toString();
      }
      if (author != null && author.isNotEmpty) {
        queryParams['author'] = author;
      }
      if (sortBy != null && sortBy.isNotEmpty) {
        queryParams['sort_by'] = sortBy;
      }
      if (sortOrder != null && sortOrder.isNotEmpty) {
        queryParams['sort_order'] = sortOrder;
      }

      final response = await apiClient.get(
        '/quotes/',
        queryParameters: queryParams,
      );

      final quotes = response['quotes'] as List;
      return quotes.map((json) => QuoteModel.fromJson(json as Map<String, dynamic>)).toList();
    } catch (e) {
      throw ServerException(
        message: 'Failed to fetch quotes: ${e.toString()}',
        code: 500,
      );
    }
  }

  @override
  Future<QuoteModel> getQuoteById(int id) async {
    try {
      final response = await apiClient.get('/quotes/$id');
      return QuoteModel.fromJson(response);
    } catch (e) {
      throw ServerException(
        message: 'Failed to fetch quote: ${e.toString()}',
        code: 500,
      );
    }
  }

  @override
  Future<List<CategoryModel>> getCategories() async {
    try {
      final response = await apiClient.get('/categories/');
      final categories = response as List;
      return categories.map((json) => CategoryModel.fromJson(json as Map<String, dynamic>)).toList();
    } catch (e) {
      throw ServerException(
        message: 'Failed to fetch categories: ${e.toString()}',
        code: 500,
      );
    }
  }

  @override
  Future<List<CategoryModel>> getActiveCategories() async {
    try {
      final response = await apiClient.get('/categories/active');
      final categories = response as List;
      return categories.map((json) => CategoryModel.fromJson(json as Map<String, dynamic>)).toList();
    } catch (e) {
      throw ServerException(
        message: 'Failed to fetch active categories: ${e.toString()}',
        code: 500,
      );
    }
  }

  @override
  Future<void> recordQuoteInteraction(int quoteId, String interactionType) async {
    try {
      await apiClient.post(
        '/quotes/$quoteId/interact',
        body: {
          'interaction_type': interactionType,
        },
      );
    } catch (e) {
      // Don't throw error for analytics - it's not critical
      // LoggingService.warning('Failed to record quote interaction: $e');
    }
  }
}
