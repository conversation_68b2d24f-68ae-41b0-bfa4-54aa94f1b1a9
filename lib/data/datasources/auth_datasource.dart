import '../../core/network/api_client.dart';
import '../../core/services/auth_service.dart';
import '../../core/errors/exceptions.dart';

abstract class AuthDataSource {
  Future<Map<String, dynamic>> login(String username, String password);
  Future<Map<String, dynamic>> register({
    required String email,
    required String username,
    required String password,
    required String fullName,
  });
  Future<Map<String, dynamic>> getCurrentUser();
  Future<void> logout();
  Future<Map<String, dynamic>> refreshToken();
}

class AuthDataSourceImpl implements AuthDataSource {
  final ApiClient apiClient;
  final AuthService authService;

  AuthDataSourceImpl({
    required this.apiClient,
    required this.authService,
  });

  @override
  Future<Map<String, dynamic>> login(String username, String password) async {
    try {
      final response = await apiClient.post(
        '/auth/login',
        body: {
          'username': username,
          'password': password,
        },
      );

      // Save token and user data
      final token = response['access_token'] as String;
      final userData = response['user'] as Map<String, dynamic>;
      
      await authService.saveToken(token);
      await authService.saveUserData(userData);
      
      if (response['refresh_token'] != null) {
        await authService.saveRefreshToken(response['refresh_token'] as String);
      }

      return response;
    } catch (e) {
      throw ServerException(
        message: 'Login failed: ${e.toString()}',
        code: 401,
      );
    }
  }

  @override
  Future<Map<String, dynamic>> register({
    required String email,
    required String username,
    required String password,
    required String fullName,
  }) async {
    try {
      final response = await apiClient.post(
        '/auth/register',
        body: {
          'email': email,
          'username': username,
          'password': password,
          'full_name': fullName,
        },
      );

      // Save token and user data if registration includes auto-login
      if (response['access_token'] != null) {
        final token = response['access_token'] as String;
        final userData = response['user'] as Map<String, dynamic>;
        
        await authService.saveToken(token);
        await authService.saveUserData(userData);
        
        if (response['refresh_token'] != null) {
          await authService.saveRefreshToken(response['refresh_token'] as String);
        }
      }

      return response;
    } catch (e) {
      throw ServerException(
        message: 'Registration failed: ${e.toString()}',
        code: 400,
      );
    }
  }

  @override
  Future<Map<String, dynamic>> getCurrentUser() async {
    try {
      final response = await apiClient.get('/auth/me');
      
      // Update stored user data
      await authService.saveUserData(response);
      
      return response;
    } catch (e) {
      throw ServerException(
        message: 'Failed to get current user: ${e.toString()}',
        code: 401,
      );
    }
  }

  @override
  Future<void> logout() async {
    try {
      // Try to logout on server
      await apiClient.post('/auth/logout');
    } catch (e) {
      // Continue with local logout even if server logout fails
      // LoggingService.warning('Server logout failed: $e');
    } finally {
      // Always clear local auth data
      await authService.clearAuth();
    }
  }

  @override
  Future<Map<String, dynamic>> refreshToken() async {
    try {
      final refreshToken = await authService.getRefreshToken();
      if (refreshToken == null) {
        throw ServerException(
          message: 'No refresh token available',
          code: 401,
        );
      }

      final response = await apiClient.post(
        '/auth/refresh',
        body: {
          'refresh_token': refreshToken,
        },
      );

      // Save new token
      final newToken = response['access_token'] as String;
      await authService.saveToken(newToken);
      
      if (response['refresh_token'] != null) {
        await authService.saveRefreshToken(response['refresh_token'] as String);
      }

      return response;
    } catch (e) {
      // Clear auth data if refresh fails
      await authService.clearAuth();
      throw ServerException(
        message: 'Token refresh failed: ${e.toString()}',
        code: 401,
      );
    }
  }
}
