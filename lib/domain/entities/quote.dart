import 'package:equatable/equatable.dart';

class Quote extends Equatable {
  final int id;
  final String text;
  final String author;
  final String? source;
  final int categoryId;
  final List<String> tags;
  final String language;
  final int length;
  final int wordCount;
  final int readingTime;
  final bool isApproved;
  final bool isFavorite;
  final bool isFeatured;
  final double qualityScore;
  final String? sourceUrl;
  final bool attributionRequired;
  final String? copyrightInfo;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? lastShown;

  const Quote({
    required this.id,
    required this.text,
    required this.author,
    this.source,
    required this.categoryId,
    this.tags = const [],
    this.language = 'en',
    required this.length,
    required this.wordCount,
    required this.readingTime,
    this.isApproved = true,
    this.isFavorite = false,
    this.isFeatured = false,
    this.qualityScore = 0.0,
    this.sourceUrl,
    this.attributionRequired = false,
    this.copyrightInfo,
    required this.createdAt,
    required this.updatedAt,
    this.lastShown,
  });

  Quote copyWith({
    int? id,
    String? text,
    String? author,
    String? source,
    int? categoryId,
    List<String>? tags,
    String? language,
    int? length,
    int? wordCount,
    int? readingTime,
    bool? isApproved,
    bool? isFavorite,
    bool? isFeatured,
    double? qualityScore,
    String? sourceUrl,
    bool? attributionRequired,
    String? copyrightInfo,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastShown,
  }) {
    return Quote(
      id: id ?? this.id,
      text: text ?? this.text,
      author: author ?? this.author,
      source: source ?? this.source,
      categoryId: categoryId ?? this.categoryId,
      tags: tags ?? this.tags,
      language: language ?? this.language,
      length: length ?? this.length,
      wordCount: wordCount ?? this.wordCount,
      readingTime: readingTime ?? this.readingTime,
      isApproved: isApproved ?? this.isApproved,
      isFavorite: isFavorite ?? this.isFavorite,
      isFeatured: isFeatured ?? this.isFeatured,
      qualityScore: qualityScore ?? this.qualityScore,
      sourceUrl: sourceUrl ?? this.sourceUrl,
      attributionRequired: attributionRequired ?? this.attributionRequired,
      copyrightInfo: copyrightInfo ?? this.copyrightInfo,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastShown: lastShown ?? this.lastShown,
    );
  }

  @override
  List<Object?> get props => [
        id,
        text,
        author,
        source,
        categoryId,
        tags,
        language,
        length,
        wordCount,
        readingTime,
        isApproved,
        isFavorite,
        isFeatured,
        qualityScore,
        sourceUrl,
        attributionRequired,
        copyrightInfo,
        createdAt,
        updatedAt,
        lastShown,
      ];

  @override
  String toString() {
    return 'Quote(id: $id, text: $text, author: $author, source: $source, categoryId: $categoryId, tags: $tags, language: $language, length: $length, wordCount: $wordCount, readingTime: $readingTime, isApproved: $isApproved, isFavorite: $isFavorite, isFeatured: $isFeatured, qualityScore: $qualityScore, sourceUrl: $sourceUrl, attributionRequired: $attributionRequired, copyrightInfo: $copyrightInfo, createdAt: $createdAt, updatedAt: $updatedAt, lastShown: $lastShown)';
  }
}
