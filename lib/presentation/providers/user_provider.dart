import 'package:flutter/material.dart';
import '../../core/services/storage_service.dart';
import '../../core/services/logging_service.dart';
import '../../core/utils/platform_utils.dart';

/// Production-level user profile provider with data persistence
class UserProvider extends ChangeNotifier {
  String _userName = '';
  String _userEmail = '';
  String _avatarPath = '';
  DateTime? _joinDate;
  Map<String, dynamic> _userSettings = {};
  bool _isFirstTime = true;
  bool _hasCompletedOnboarding = false;
  
  // Getters
  String get userName => _userName;
  String get userEmail => _userEmail;
  String get avatarPath => _avatarPath;
  DateTime? get joinDate => _joinDate;
  Map<String, dynamic> get userSettings => Map.unmodifiable(_userSettings);
  bool get isFirstTime => _isFirstTime;
  bool get hasCompletedOnboarding => _hasCompletedOnboarding;
  
  /// Initialize user provider and load data
  Future<void> initialize() async {
    try {
      await _loadUserData();
      LoggingService.info('UserProvider initialized');
    } catch (e) {
      LoggingService.error('Failed to initialize UserProvider', error: e);
    }
  }
  
  /// Load user data from storage
  Future<void> _loadUserData() async {
    try {
      final userData = StorageService.loadUserData();
      
      if (userData.isNotEmpty) {
        _userName = userData['userName'] ?? '';
        _userEmail = userData['userEmail'] ?? '';
        _avatarPath = userData['avatarPath'] ?? '';
        _joinDate = userData['joinDate'] != null 
            ? DateTime.parse(userData['joinDate'])
            : null;
        _userSettings = Map<String, dynamic>.from(userData['userSettings'] ?? {});
        _isFirstTime = userData['isFirstTime'] ?? true;
        _hasCompletedOnboarding = userData['hasCompletedOnboarding'] ?? false;
      } else {
        // First time user
        _setDefaultValues();
      }
      
      notifyListeners();
    } catch (e) {
      LoggingService.error('Failed to load user data', error: e);
      _setDefaultValues();
    }
  }
  
  /// Set default values for new users
  void _setDefaultValues() {
    _userName = '';
    _userEmail = '';
    _avatarPath = '';
    _joinDate = DateTime.now();
    _userSettings = _getDefaultSettings();
    _isFirstTime = true;
    _hasCompletedOnboarding = false;
  }
  
  /// Get default user settings
  Map<String, dynamic> _getDefaultSettings() {
    return {
      'language': 'en',
      'enableAnalytics': true,
      'enableCrashReporting': true,
      'autoBackup': true,
      'shareUsageData': false,
      'enableHapticFeedback': PlatformUtils.isMobile,
      'enableSounds': true,
      'enableAnimations': true,
    };
  }
  
  /// Save user data to storage
  Future<bool> _saveUserData() async {
    try {
      final userData = {
        'userName': _userName,
        'userEmail': _userEmail,
        'avatarPath': _avatarPath,
        'joinDate': _joinDate?.toIso8601String(),
        'userSettings': _userSettings,
        'isFirstTime': _isFirstTime,
        'hasCompletedOnboarding': _hasCompletedOnboarding,
        'lastUpdated': DateTime.now().toIso8601String(),
        'platform': PlatformUtils.platformName,
      };
      
      final success = await StorageService.saveUserData(userData);
      
      if (success) {
        LoggingService.info('User data saved successfully');
      } else {
        LoggingService.warning('Failed to save user data');
      }
      
      return success;
    } catch (e) {
      LoggingService.error('Error saving user data', error: e);
      return false;
    }
  }
  
  /// Update user name
  Future<bool> updateUserName(String name) async {
    if (name.trim().isEmpty) return false;
    
    _userName = name.trim();
    notifyListeners();
    
    LoggingService.logUserAction('update_user_name');
    return await _saveUserData();
  }
  
  /// Update user email
  Future<bool> updateUserEmail(String email) async {
    if (email.trim().isEmpty || !_isValidEmail(email)) return false;
    
    _userEmail = email.trim();
    notifyListeners();
    
    LoggingService.logUserAction('update_user_email');
    return await _saveUserData();
  }
  
  /// Update avatar path
  Future<bool> updateAvatarPath(String path) async {
    _avatarPath = path;
    notifyListeners();
    
    LoggingService.logUserAction('update_avatar');
    return await _saveUserData();
  }
  
  /// Update user setting
  Future<bool> updateSetting(String key, dynamic value) async {
    _userSettings[key] = value;
    notifyListeners();
    
    LoggingService.logUserAction('update_setting', data: {
      'setting': key,
      'value': value.toString(),
    });
    
    return await _saveUserData();
  }
  
  /// Get user setting with default value
  T getSetting<T>(String key, T defaultValue) {
    return _userSettings[key] as T? ?? defaultValue;
  }
  
  /// Mark onboarding as completed
  Future<bool> completeOnboarding() async {
    _hasCompletedOnboarding = true;
    _isFirstTime = false;
    notifyListeners();
    
    LoggingService.logUserAction('complete_onboarding');
    return await _saveUserData();
  }
  
  /// Reset user data (for account deletion or reset)
  Future<bool> resetUserData() async {
    try {
      _setDefaultValues();
      notifyListeners();
      
      final success = await _saveUserData();
      
      LoggingService.logUserAction('reset_user_data');
      return success;
    } catch (e) {
      LoggingService.error('Failed to reset user data', error: e);
      return false;
    }
  }
  
  /// Export user profile data
  Map<String, dynamic> exportUserProfile() {
    return {
      'userName': _userName,
      'userEmail': _userEmail,
      'avatarPath': _avatarPath,
      'joinDate': _joinDate?.toIso8601String(),
      'userSettings': _userSettings,
      'hasCompletedOnboarding': _hasCompletedOnboarding,
      'exportTimestamp': DateTime.now().toIso8601String(),
      'platform': PlatformUtils.platformName,
    };
  }
  
  /// Import user profile data
  Future<bool> importUserProfile(Map<String, dynamic> profileData) async {
    try {
      _userName = profileData['userName'] ?? '';
      _userEmail = profileData['userEmail'] ?? '';
      _avatarPath = profileData['avatarPath'] ?? '';
      _joinDate = profileData['joinDate'] != null 
          ? DateTime.parse(profileData['joinDate'])
          : DateTime.now();
      _userSettings = Map<String, dynamic>.from(profileData['userSettings'] ?? {});
      _hasCompletedOnboarding = profileData['hasCompletedOnboarding'] ?? false;
      _isFirstTime = false;
      
      notifyListeners();
      
      LoggingService.logUserAction('import_user_profile');
      return await _saveUserData();
    } catch (e) {
      LoggingService.error('Failed to import user profile', error: e);
      return false;
    }
  }
  
  /// Get user statistics
  Map<String, dynamic> getUserStats() {
    final daysSinceJoin = _joinDate != null 
        ? DateTime.now().difference(_joinDate!).inDays
        : 0;
    
    return {
      'userName': _userName,
      'joinDate': _joinDate?.toIso8601String(),
      'daysSinceJoin': daysSinceJoin,
      'hasCompletedOnboarding': _hasCompletedOnboarding,
      'settingsCount': _userSettings.length,
      'platform': PlatformUtils.platformName,
    };
  }
  
  /// Validate email format
  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }
  
  /// Check if user profile is complete
  bool get isProfileComplete {
    return _userName.isNotEmpty && 
           _hasCompletedOnboarding &&
           _joinDate != null;
  }
  
  /// Get user display name (fallback to email or default)
  String get displayName {
    if (_userName.isNotEmpty) return _userName;
    if (_userEmail.isNotEmpty) return _userEmail.split('@').first;
    return 'User';
  }
  
  /// Get user initials for avatar fallback
  String get userInitials {
    final name = displayName;
    if (name.length >= 2) {
      return name.substring(0, 2).toUpperCase();
    }
    return name.substring(0, 1).toUpperCase();
  }
  
  /// Check if user has enabled a specific feature
  bool isFeatureEnabled(String feature) {
    return getSetting<bool>(feature, false);
  }
  
  /// Get user's preferred language
  String get preferredLanguage {
    return getSetting<String>('language', 'en');
  }
  
  /// Check if analytics are enabled
  bool get analyticsEnabled {
    return getSetting<bool>('enableAnalytics', true);
  }
  
  /// Check if crash reporting is enabled
  bool get crashReportingEnabled {
    return getSetting<bool>('enableCrashReporting', true);
  }
}
