import 'package:flutter/material.dart';
import '../../core/services/image_service.dart';
import 'analytics_provider.dart';
import 'quote_provider.dart';
import 'preferences_provider.dart';

enum AchievementType {
  firstQuote,
  streak3,
  streak7,
  streak30,
  categoryExplorer,
  quoteMaster,
  earlyBird,
  nightOwl,
  weekendWarrior,
  perfectWeek,
}

class Achievement {
  final AchievementType type;
  final String title;
  final String description;
  final IconData icon;
  final Color color;
  final int points;
  final bool isUnlocked;
  final DateTime? unlockedAt;

  const Achievement({
    required this.type,
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    required this.points,
    this.isUnlocked = false,
    this.unlockedAt,
  });

  Achievement copyWith({
    bool? isUnlocked,
    DateTime? unlockedAt,
  }) {
    return Achievement(
      type: type,
      title: title,
      description: description,
      icon: icon,
      color: color,
      points: points,
      isUnlocked: isUnlocked ?? this.isUnlocked,
      unlockedAt: unlockedAt ?? this.unlockedAt,
    );
  }
}

/// Gamification provider that depends on AnalyticsProvider, QuoteProvider, and PreferencesProvider
/// This demonstrates complex ProxyProvider usage with multiple dependencies
class GamificationProvider extends ChangeNotifier {
  final AnalyticsProvider _analyticsProvider;
  final QuoteProvider _quoteProvider;
  final PreferencesProvider _preferencesProvider;

  GamificationProvider({
    required AnalyticsProvider analyticsProvider,
    required QuoteProvider quoteProvider,
    required PreferencesProvider preferencesProvider,
  })  : _analyticsProvider = analyticsProvider,
        _quoteProvider = quoteProvider,
        _preferencesProvider = preferencesProvider {
    // Listen to changes in dependencies
    _analyticsProvider.addListener(_onAnalyticsChanged);
    _quoteProvider.addListener(_onQuoteChanged);
    _preferencesProvider.addListener(_onPreferencesChanged);
    _initializeAchievements();
    _checkAchievements();
  }

  // Gamification state
  Map<AchievementType, Achievement> _achievements = {};
  int _totalPoints = 0;
  int _level = 1;
  int _experiencePoints = 0;
  int _experienceToNextLevel = 100;
  List<Achievement> _recentlyUnlocked = [];
  final Map<String, int> _dailyChallenges = {};
  bool _hasNewAchievements = false;

  // Getters
  Map<AchievementType, Achievement> get achievements => Map.unmodifiable(_achievements);
  List<Achievement> get unlockedAchievements => 
      _achievements.values.where((a) => a.isUnlocked).toList();
  List<Achievement> get lockedAchievements => 
      _achievements.values.where((a) => !a.isUnlocked).toList();
  int get totalPoints => _totalPoints;
  int get level => _level;
  int get experiencePoints => _experiencePoints;
  int get experienceToNextLevel => _experienceToNextLevel;
  List<Achievement> get recentlyUnlocked => List.unmodifiable(_recentlyUnlocked);
  bool get hasNewAchievements => _hasNewAchievements;
  
  double get levelProgress => _experiencePoints / (_experiencePoints + _experienceToNextLevel);
  int get achievementCount => unlockedAchievements.length;
  double get completionPercentage => (achievementCount / _achievements.length) * 100;

  // Initialize all achievements
  void _initializeAchievements() {
    _achievements = {
      AchievementType.firstQuote: const Achievement(
        type: AchievementType.firstQuote,
        title: 'First Steps',
        description: 'Read your first motivational quote',
        icon: Icons.star,
        color: Colors.amber,
        points: 10,
      ),
      AchievementType.streak3: const Achievement(
        type: AchievementType.streak3,
        title: 'Getting Started',
        description: 'Maintain a 3-day reading streak',
        icon: Icons.local_fire_department,
        color: Colors.orange,
        points: 25,
      ),
      AchievementType.streak7: const Achievement(
        type: AchievementType.streak7,
        title: 'Week Warrior',
        description: 'Maintain a 7-day reading streak',
        icon: Icons.whatshot,
        color: Colors.deepOrange,
        points: 50,
      ),
      AchievementType.streak30: const Achievement(
        type: AchievementType.streak30,
        title: 'Motivation Master',
        description: 'Maintain a 30-day reading streak',
        icon: Icons.emoji_events,
        color: Colors.purple,
        points: 200,
      ),
      AchievementType.categoryExplorer: const Achievement(
        type: AchievementType.categoryExplorer,
        title: 'Category Explorer',
        description: 'Read quotes from 5 different categories',
        icon: Icons.explore,
        color: Colors.teal,
        points: 30,
      ),
      AchievementType.quoteMaster: const Achievement(
        type: AchievementType.quoteMaster,
        title: 'Quote Master',
        description: 'Read 100 motivational quotes',
        icon: Icons.school,
        color: Colors.indigo,
        points: 100,
      ),
      AchievementType.earlyBird: const Achievement(
        type: AchievementType.earlyBird,
        title: 'Early Bird',
        description: 'Read quotes before 8 AM for 5 days',
        icon: Icons.wb_sunny,
        color: Colors.yellow,
        points: 40,
      ),
      AchievementType.nightOwl: const Achievement(
        type: AchievementType.nightOwl,
        title: 'Night Owl',
        description: 'Read quotes after 10 PM for 5 days',
        icon: Icons.nightlight,
        color: Colors.deepPurple,
        points: 40,
      ),
      AchievementType.weekendWarrior: const Achievement(
        type: AchievementType.weekendWarrior,
        title: 'Weekend Warrior',
        description: 'Read quotes on 4 consecutive weekends',
        icon: Icons.weekend,
        color: Colors.green,
        points: 60,
      ),
      AchievementType.perfectWeek: const Achievement(
        type: AchievementType.perfectWeek,
        title: 'Perfect Week',
        description: 'Read quotes every day for a week',
        icon: Icons.check_circle,
        color: Colors.blue,
        points: 75,
      ),
    };
  }

  // React to analytics changes
  void _onAnalyticsChanged() {
    _checkAchievements();
    _updateLevel();
  }

  // React to quote changes
  void _onQuoteChanged() {
    if (_quoteProvider.status == QuoteStatus.loaded && _quoteProvider.currentQuote != null) {
      _addExperience(5); // 5 XP per quote read
    }
  }

  // React to preferences changes
  void _onPreferencesChanged() {
    // Could trigger achievements based on preferences
    _checkAchievements();
  }

  // Check and unlock achievements
  void _checkAchievements() {
    final newlyUnlocked = <Achievement>[];

    // First quote achievement
    if (!_achievements[AchievementType.firstQuote]!.isUnlocked && 
        _analyticsProvider.totalQuotesViewed > 0) {
      newlyUnlocked.add(_unlockAchievement(AchievementType.firstQuote));
    }

    // Streak achievements
    final currentStreak = _analyticsProvider.currentStreak;
    if (!_achievements[AchievementType.streak3]!.isUnlocked && currentStreak >= 3) {
      newlyUnlocked.add(_unlockAchievement(AchievementType.streak3));
    }
    if (!_achievements[AchievementType.streak7]!.isUnlocked && currentStreak >= 7) {
      newlyUnlocked.add(_unlockAchievement(AchievementType.streak7));
    }
    if (!_achievements[AchievementType.streak30]!.isUnlocked && currentStreak >= 30) {
      newlyUnlocked.add(_unlockAchievement(AchievementType.streak30));
    }

    // Category explorer achievement
    if (!_achievements[AchievementType.categoryExplorer]!.isUnlocked && 
        _analyticsProvider.categoryStats.length >= 5) {
      newlyUnlocked.add(_unlockAchievement(AchievementType.categoryExplorer));
    }

    // Quote master achievement
    if (!_achievements[AchievementType.quoteMaster]!.isUnlocked && 
        _analyticsProvider.totalQuotesViewed >= 100) {
      newlyUnlocked.add(_unlockAchievement(AchievementType.quoteMaster));
    }

    // Add newly unlocked achievements to recent list
    if (newlyUnlocked.isNotEmpty) {
      _recentlyUnlocked.addAll(newlyUnlocked);
      _hasNewAchievements = true;
      
      // Keep only last 5 recent achievements
      if (_recentlyUnlocked.length > 5) {
        _recentlyUnlocked = _recentlyUnlocked.skip(_recentlyUnlocked.length - 5).toList();
      }
      
      notifyListeners();
    }
  }

  // Unlock an achievement
  Achievement _unlockAchievement(AchievementType type) {
    final achievement = _achievements[type]!;
    final unlockedAchievement = achievement.copyWith(
      isUnlocked: true,
      unlockedAt: DateTime.now(),
    );
    
    _achievements[type] = unlockedAchievement;
    _totalPoints += achievement.points;
    _addExperience(achievement.points);
    
    return unlockedAchievement;
  }

  // Add experience points
  void _addExperience(int points) {
    _experiencePoints += points;
    _updateLevel();
  }

  // Update level based on experience
  void _updateLevel() {
    final newLevel = (_experiencePoints / 100).floor() + 1;
    if (newLevel > _level) {
      _level = newLevel;
      _experienceToNextLevel = (newLevel * 100) - _experiencePoints;
      // Could trigger level-up celebration here
    } else {
      _experienceToNextLevel = (_level * 100) - _experiencePoints;
    }
  }

  // Mark new achievements as seen
  void markAchievementsAsSeen() {
    _hasNewAchievements = false;
    notifyListeners();
  }

  // Clear recent achievements
  void clearRecentAchievements() {
    _recentlyUnlocked.clear();
    _hasNewAchievements = false;
    notifyListeners();
  }

  // Get daily challenge
  String getDailyChallenge() {
    final challenges = [
      'Read 3 quotes today',
      'Explore a new category',
      'Share a quote with someone',
      'Reflect on your favorite quote',
      'Set a daily intention',
    ];
    
    final today = DateTime.now();
    final dayOfYear = today.difference(DateTime(today.year, 1, 1)).inDays;
    return challenges[dayOfYear % challenges.length];
  }

  // Get motivational message based on progress
  String getMotivationalMessage() {
    if (_analyticsProvider.currentStreak == 0) {
      return 'Start your motivation journey today! 🌟';
    } else if (_analyticsProvider.currentStreak < 3) {
      return 'Keep going! You\'re building a great habit! 💪';
    } else if (_analyticsProvider.currentStreak < 7) {
      return 'Amazing streak! You\'re on fire! 🔥';
    } else if (_analyticsProvider.currentStreak < 30) {
      return 'Incredible dedication! You\'re a motivation master! 🏆';
    } else {
      return 'Legendary! You\'re an inspiration to others! 👑';
    }
  }

  // Get achievement widget with image
  Widget getAchievementWidget(AchievementType type, double size) {
    final achievement = _achievements[type];
    if (achievement == null) {
      return SizedBox(width: size, height: size);
    }

    return ImageService.buildAchievementBadge(
      achievementKey: type.toString().split('.').last,
      size: size,
      fallbackColor: achievement.color,
      fallbackIcon: achievement.icon,
      isUnlocked: achievement.isUnlocked,
    );
  }

  // Reset gamification data
  void resetGamification() {
    _initializeAchievements();
    _totalPoints = 0;
    _level = 1;
    _experiencePoints = 0;
    _experienceToNextLevel = 100;
    _recentlyUnlocked.clear();
    _dailyChallenges.clear();
    _hasNewAchievements = false;
    notifyListeners();
  }

  @override
  void dispose() {
    _analyticsProvider.removeListener(_onAnalyticsChanged);
    _quoteProvider.removeListener(_onQuoteChanged);
    _preferencesProvider.removeListener(_onPreferencesChanged);
    super.dispose();
  }
}
