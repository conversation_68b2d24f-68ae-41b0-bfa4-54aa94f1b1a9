import 'package:flutter/material.dart';
import '../../core/usecases/usecase.dart';
import '../../core/utils/time_utils.dart';
import '../../domain/entities/user_preferences.dart';
import '../../domain/usecases/get_user_preferences.dart';
import '../../domain/usecases/save_user_preferences.dart';

class PreferencesProvider extends ChangeNotifier {
  final GetUserPreferences getUserPreferences;
  final SaveUserPreferences saveUserPreferences;

  PreferencesProvider({
    required this.getUserPreferences,
    required this.saveUserPreferences,
  });

  UserPreferences? _preferences;
  bool _isLoading = false;
  String _errorMessage = '';

  // Getters
  List<String> get selectedCategories => _preferences?.selectedCategories ?? [];
  bool get notificationEnabled => _preferences?.notificationEnabled ?? true;
  List<TimeOfDay> get notificationTimes =>
      _preferences?.notificationTimes ?? [const TimeOfDay(hour: 9, minute: 0)];
  int get notificationCount => _preferences?.notificationCount ?? 3;
  bool get onboardingComplete => _preferences?.onboardingComplete ?? false;
  bool get isLoading => _isLoading;
  String get errorMessage => _errorMessage;

  // Initialize preferences from storage
  Future<void> loadPreferences() async {
    _isLoading = true;
    _errorMessage = '';
    notifyListeners();

    try {
      final result = await getUserPreferences(NoParams());
      result.fold(
        (failure) {
          _errorMessage = failure.message;
          // Set default preferences on failure
          _preferences = UserPreferences(
            selectedCategories: const ['motivation', 'success', 'inspiration'],
            notificationEnabled: true,
            notificationTimes: const [TimeOfDay(hour: 9, minute: 0)],
            notificationCount: 3,
            onboardingComplete: false,
            lastQuoteDate: DateTime.now(),
          );
        },
        (preferences) {
          _preferences = preferences;
        },
      );
    } catch (e) {
      _errorMessage = 'Unexpected error: ${e.toString()}';
      debugPrint('Error loading preferences: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Toggle category selection
  Future<void> toggleCategory(String category) async {
    if (_preferences == null) return;

    final currentCategories = List<String>.from(_preferences!.selectedCategories);
    if (currentCategories.contains(category)) {
      currentCategories.remove(category);
    } else {
      currentCategories.add(category);
    }

    await _updatePreferences(_preferences!.copyWith(selectedCategories: currentCategories));
  }

  // Set multiple categories at once
  Future<void> setSelectedCategories(List<String> categories) async {
    if (_preferences == null) return;
    await _updatePreferences(_preferences!.copyWith(selectedCategories: categories));
  }

  // Clear all selected categories
  Future<void> clearSelectedCategories() async {
    if (_preferences == null) return;
    await _updatePreferences(_preferences!.copyWith(selectedCategories: []));
  }

  // Toggle notification enabled/disabled
  Future<void> toggleNotifications() async {
    if (_preferences == null) return;
    await _updatePreferences(_preferences!.copyWith(notificationEnabled: !_preferences!.notificationEnabled));
  }

  // Set notification times
  Future<void> setNotificationTimes(List<TimeOfDay> times) async {
    if (_preferences == null) return;
    await _updatePreferences(_preferences!.copyWith(notificationTimes: times));
  }

  // Set notification count
  Future<void> setNotificationCount(int count) async {
    if (_preferences == null) return;
    final newTimes = TimeUtils.generateDefaultNotificationTimes(count);
    await _updatePreferences(_preferences!.copyWith(
      notificationCount: count,
      notificationTimes: newTimes,
    ));
  }

  // Complete onboarding
  Future<void> completeOnboarding() async {
    if (_preferences == null) return;
    await _updatePreferences(_preferences!.copyWith(onboardingComplete: true));
  }

  // Private helper method for updating preferences
  Future<void> _updatePreferences(UserPreferences newPreferences) async {
    try {
      final result = await saveUserPreferences(SaveUserPreferencesParams(preferences: newPreferences));
      result.fold(
        (failure) {
          _errorMessage = failure.message;
          debugPrint('Error saving preferences: ${failure.message}');
        },
        (_) {
          _preferences = newPreferences;
          _errorMessage = '';
        },
      );
      notifyListeners();
    } catch (e) {
      _errorMessage = 'Unexpected error: ${e.toString()}';
      debugPrint('Error updating preferences: $e');
      notifyListeners();
    }
  }

  // Check if at least one category is selected
  bool get hasSelectedCategories => selectedCategories.isNotEmpty;

  // Get a random selected category
  String? getRandomSelectedCategory() {
    if (selectedCategories.isEmpty) return null;
    final categories = List<String>.from(selectedCategories);
    categories.shuffle();
    return categories.first;
  }

  // Set notification time for a specific index (for backward compatibility)
  Future<void> setNotificationTime(int index, TimeOfDay time) async {
    if (_preferences == null) return;

    final currentTimes = List<TimeOfDay>.from(_preferences!.notificationTimes);

    // Extend the list if necessary
    while (currentTimes.length <= index) {
      currentTimes.add(const TimeOfDay(hour: 9, minute: 0));
    }

    if (index >= 0 && index < currentTimes.length) {
      currentTimes[index] = time;
      await setNotificationTimes(currentTimes);
    }
  }

  // Add a new notification time
  Future<void> addNotificationTime(TimeOfDay time) async {
    if (_preferences == null) return;

    final currentTimes = List<TimeOfDay>.from(_preferences!.notificationTimes);
    currentTimes.add(time);
    await _updatePreferences(_preferences!.copyWith(
      notificationTimes: currentTimes,
      notificationCount: currentTimes.length,
    ));
  }

  // Remove a notification time at index
  Future<void> removeNotificationTime(int index) async {
    if (_preferences == null) return;

    final currentTimes = List<TimeOfDay>.from(_preferences!.notificationTimes);
    if (index >= 0 && index < currentTimes.length && currentTimes.length > 1) {
      currentTimes.removeAt(index);
      await _updatePreferences(_preferences!.copyWith(
        notificationTimes: currentTimes,
        notificationCount: currentTimes.length,
      ));
    }
  }

  // Reset preferences to defaults
  Future<void> resetToDefaults() async {
    final defaultPreferences = UserPreferences(
      selectedCategories: const ['motivation', 'success', 'inspiration'],
      notificationEnabled: true,
      notificationTimes: const [TimeOfDay(hour: 9, minute: 0)],
      notificationCount: 3,
      onboardingComplete: false,
      lastQuoteDate: DateTime.now(),
    );
    await _updatePreferences(defaultPreferences);
  }
}
