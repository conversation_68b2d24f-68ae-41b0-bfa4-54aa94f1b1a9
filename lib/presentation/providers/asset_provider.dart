import 'package:flutter/foundation.dart';
import '../../core/models/asset_models.dart';
import '../../core/services/asset_management_service.dart';
import '../../core/services/logging_service.dart';

/// Provider for managing asset state and operations
class AssetProvider extends ChangeNotifier {
  final AssetManagementService _assetService;
  
  Map<AssetCategory, List<LocalAsset>> _assets = {};
  bool _isLoading = false;
  String? _error;
  
  AssetProvider(this._assetService);

  // Getters
  Map<AssetCategory, List<LocalAsset>> get assets => Map.unmodifiable(_assets);
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  /// Get assets for a specific category
  List<LocalAsset> getAssetsForCategory(AssetCategory category) {
    return _assets[category] ?? [];
  }
  
  /// Get all assets as a flat list
  List<LocalAsset> get allAssets {
    final allAssets = <LocalAsset>[];
    for (final categoryAssets in _assets.values) {
      allAssets.addAll(categoryAssets);
    }
    return allAssets;
  }
  
  /// Get favorite assets
  List<LocalAsset> get favoriteAssets {
    return allAssets.where((asset) => asset.isFavorite).toList();
  }
  
  /// Get asset by ID
  LocalAsset? getAssetById(String id) {
    for (final categoryAssets in _assets.values) {
      for (final asset in categoryAssets) {
        if (asset.id == id) return asset;
      }
    }
    return null;
  }
  
  /// Get random asset from category
  LocalAsset? getRandomAsset(AssetCategory category) {
    final categoryAssets = _assets[category];
    if (categoryAssets == null || categoryAssets.isEmpty) return null;
    
    categoryAssets.shuffle();
    return categoryAssets.first;
  }
  
  /// Get assets by search query
  List<LocalAsset> searchAssets(String query) {
    if (query.trim().isEmpty) return allAssets;
    
    final lowerQuery = query.toLowerCase();
    return allAssets.where((asset) {
      return asset.fileName.toLowerCase().contains(lowerQuery) ||
             asset.metadata?.title?.toLowerCase().contains(lowerQuery) == true ||
             asset.metadata?.tags?.any((tag) => tag.toLowerCase().contains(lowerQuery)) == true;
    }).toList();
  }
  
  /// Load assets from the asset management service
  Future<void> loadAssets() async {
    if (_isLoading) return;
    
    _setLoading(true);
    _setError(null);
    
    try {
      // Ensure asset service is initialized
      if (!_assetService.isInitialized) {
        await _assetService.initialize();
      }
      
      // Load all assets
      _assets = _assetService.getAllAssets();
      
      LoggingService.info('Loaded ${allAssets.length} assets');
      notifyListeners();
      
    } catch (e, stackTrace) {
      _setError('Failed to load assets: $e');
      LoggingService.error(
        'Failed to load assets',
        error: e,
        stackTrace: stackTrace,
      );
    } finally {
      _setLoading(false);
    }
  }
  
  /// Download default assets
  Future<void> downloadDefaultAssets() async {
    if (_isLoading) return;
    
    _setLoading(true);
    _setError(null);
    
    try {
      // This would trigger the asset service to download new assets
      // For now, we'll just reload the existing assets
      await _assetService.initialize();
      _assets = _assetService.getAllAssets();
      
      LoggingService.info('Downloaded and loaded default assets');
      notifyListeners();
      
    } catch (e, stackTrace) {
      _setError('Failed to download assets: $e');
      LoggingService.error(
        'Failed to download assets',
        error: e,
        stackTrace: stackTrace,
      );
    } finally {
      _setLoading(false);
    }
  }
  
  /// Add a custom asset
  Future<LocalAsset?> addCustomAsset(
    String sourcePath,
    AssetCategory category, {
    AssetMetadata? metadata,
  }) async {
    try {
      final asset = await _assetService.addCustomAsset(
        sourcePath,
        category,
        metadata: metadata,
      );
      
      // Update local state
      _assets[category] = [...(_assets[category] ?? []), asset];
      notifyListeners();
      
      LoggingService.logUserAction('add_custom_asset', data: {
        'category': category.name,
        'file_name': asset.fileName,
      });
      
      return asset;
      
    } catch (e, stackTrace) {
      LoggingService.error(
        'Failed to add custom asset',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }
  
  /// Toggle favorite status of an asset
  Future<void> toggleFavorite(LocalAsset asset) async {
    try {
      final updatedAsset = asset.copyWith(isFavorite: !asset.isFavorite);
      
      // Update in local state
      final categoryAssets = _assets[asset.category];
      if (categoryAssets != null) {
        final index = categoryAssets.indexWhere((a) => a.id == asset.id);
        if (index != -1) {
          categoryAssets[index] = updatedAsset;
          notifyListeners();
        }
      }
      
      LoggingService.logUserAction('toggle_asset_favorite', data: {
        'asset_id': asset.id,
        'is_favorite': updatedAsset.isFavorite,
      });
      
    } catch (e, stackTrace) {
      LoggingService.error(
        'Failed to toggle asset favorite',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }
  
  /// Delete an asset
  Future<void> deleteAsset(LocalAsset asset) async {
    try {
      await _assetService.deleteAsset(asset);
      
      // Update local state
      final categoryAssets = _assets[asset.category];
      if (categoryAssets != null) {
        categoryAssets.removeWhere((a) => a.id == asset.id);
        notifyListeners();
      }
      
      LoggingService.logUserAction('delete_asset', data: {
        'asset_id': asset.id,
        'category': asset.category.name,
      });
      
    } catch (e, stackTrace) {
      LoggingService.error(
        'Failed to delete asset',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }
  
  /// Increment usage count for an asset
  Future<void> incrementUsageCount(LocalAsset asset) async {
    try {
      final updatedAsset = asset.copyWith(usageCount: asset.usageCount + 1);
      
      // Update in local state
      final categoryAssets = _assets[asset.category];
      if (categoryAssets != null) {
        final index = categoryAssets.indexWhere((a) => a.id == asset.id);
        if (index != -1) {
          categoryAssets[index] = updatedAsset;
          notifyListeners();
        }
      }
      
    } catch (e, stackTrace) {
      LoggingService.error(
        'Failed to increment usage count',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }
  
  /// Get most used assets
  List<LocalAsset> getMostUsedAssets({int limit = 10}) {
    final sortedAssets = allAssets.toList()
      ..sort((a, b) => b.usageCount.compareTo(a.usageCount));
    
    return sortedAssets.take(limit).toList();
  }
  
  /// Get recently added assets
  List<LocalAsset> getRecentAssets({int limit = 10}) {
    final sortedAssets = allAssets.toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
    
    return sortedAssets.take(limit).toList();
  }
  
  /// Get asset statistics
  Map<String, dynamic> getAssetStatistics() {
    final stats = <String, dynamic>{
      'total_assets': allAssets.length,
      'favorite_assets': favoriteAssets.length,
      'categories': <String, int>{},
      'total_size': 0,
    };
    
    for (final category in AssetCategory.values) {
      final categoryAssets = _assets[category] ?? [];
      stats['categories'][category.name] = categoryAssets.length;
    }
    
    for (final asset in allAssets) {
      stats['total_size'] += asset.fileSize;
    }
    
    return stats;
  }
  
  /// Clear all assets (for testing or reset)
  void clearAssets() {
    _assets.clear();
    notifyListeners();
  }
  
  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  /// Set error state
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }
}
