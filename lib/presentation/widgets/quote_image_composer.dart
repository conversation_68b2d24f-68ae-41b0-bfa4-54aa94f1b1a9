import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import '../../core/models/asset_models.dart';
import '../../core/services/sharing_service.dart';
import '../../domain/entities/quote.dart';
import '../providers/asset_provider.dart';
import '../screens/asset_browser_screen.dart';

/// Widget for composing quotes with background images
class QuoteImageComposer extends StatefulWidget {
  final Quote quote;
  final LocalAsset? initialBackground;
  final VoidCallback? onSaved;
  final VoidCallback? onShared;

  const QuoteImageComposer({
    super.key,
    required this.quote,
    this.initialBackground,
    this.onSaved,
    this.onShared,
  });

  @override
  State<QuoteImageComposer> createState() => _QuoteImageComposerState();
}

class _QuoteImageComposerState extends State<QuoteImageComposer> {
  LocalAsset? _selectedBackground;
  Color _textColor = Colors.white;
  Color? _overlayColor;
  double _overlayOpacity = 0.4;
  String _fontFamily = 'Roboto';
  double _fontSize = 24.0;
  TextAlign _textAlign = TextAlign.center;
  bool _showOverlay = true;

  @override
  void initState() {
    super.initState();
    _selectedBackground = widget.initialBackground;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Quote Image'),
        actions: [
          IconButton(
            onPressed: _shareQuoteImage,
            icon: const Icon(Icons.share),
          ),
          IconButton(
            onPressed: _saveQuoteImage,
            icon: const Icon(Icons.save),
          ),
        ],
      ),
      
      body: Column(
        children: [
          // Preview area
          Expanded(
            flex: 3,
            child: Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: _buildQuotePreview(context),
              ),
            ),
          ),
          
          // Controls area
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.all(16),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildBackgroundSelector(context),
                    const SizedBox(height: 16),
                    _buildTextControls(context),
                    const SizedBox(height: 16),
                    _buildOverlayControls(context),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuotePreview(BuildContext context) {
    final theme = Theme.of(context);
    if (_selectedBackground == null) {
      return Container(
        color: theme.colorScheme.surfaceContainerHighest,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.image_outlined,
                size: 64,
                color: theme.colorScheme.onSurfaceVariant,
              ),
              const SizedBox(height: 16),
              Text(
                'Select a background image',
                style: theme.textTheme.titleMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: 8),
              ElevatedButton(
                onPressed: _selectBackground,
                child: const Text('Choose Background'),
              ),
            ],
          ),
        ),
      );
    }

    return Stack(
      children: [
        // Background image
        Positioned.fill(
          child: Image.file(
            File(_selectedBackground!.localPath),
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                color: theme.colorScheme.errorContainer,
                child: Icon(
                  Icons.broken_image,
                  color: theme.colorScheme.onErrorContainer,
                  size: 48,
                ),
              );
            },
          ),
        ),
        
        // Overlay
        if (_showOverlay && _overlayColor != null)
          Positioned.fill(
            child: Container(
              color: _overlayColor!.withOpacity(_overlayOpacity),
            ),
          ),
        
        // Quote text
        Positioned.fill(
          child: Container(
            padding: const EdgeInsets.all(32),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '"${widget.quote.text}"',
                  style: TextStyle(
                    color: _textColor,
                    fontSize: _fontSize,
                    fontWeight: FontWeight.w600,
                    fontFamily: _fontFamily,
                    height: 1.4,
                    shadows: [
                      Shadow(
                        offset: const Offset(2, 2),
                        blurRadius: 4,
                        color: Colors.black.withOpacity(0.5),
                      ),
                    ],
                  ),
                  textAlign: _textAlign,
                ),
                const SizedBox(height: 24),
                Text(
                  '- ${widget.quote.author}',
                  style: TextStyle(
                    color: _textColor.withOpacity(0.9),
                    fontSize: _fontSize * 0.7,
                    fontWeight: FontWeight.w500,
                    fontFamily: _fontFamily,
                    shadows: [
                      Shadow(
                        offset: const Offset(1, 1),
                        blurRadius: 2,
                        color: Colors.black.withOpacity(0.5),
                      ),
                    ],
                  ),
                  textAlign: _textAlign,
                ),
              ],
            ),
          ),
        ),
        
        // Change background button
        Positioned(
          top: 16,
          right: 16,
          child: FloatingActionButton.small(
            onPressed: _selectBackground,
            backgroundColor: Colors.black.withOpacity(0.6),
            foregroundColor: Colors.white,
            child: const Icon(Icons.image),
          ),
        ),
      ],
    );
  }

  Widget _buildBackgroundSelector(BuildContext context) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Background',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 80,
          child: Consumer<AssetProvider>(
            builder: (context, assetProvider, child) {
              final backgrounds = assetProvider.getAssetsForCategory(AssetCategory.backgrounds);
              
              return ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: backgrounds.length + 1,
                itemBuilder: (context, index) {
                  if (index == 0) {
                    return _buildBackgroundOption(
                      context,
                      null,
                      'Browse All',
                      Icons.apps,
                    );
                  }

                  final background = backgrounds[index - 1];
                  return _buildBackgroundOption(
                    context,
                    background,
                    background.metadata?.title ?? background.fileName,
                    null,
                  );
                },
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildBackgroundOption(BuildContext context, LocalAsset? asset, String label, IconData? icon) {
    final theme = Theme.of(context);
    final isSelected = _selectedBackground?.id == asset?.id;

    return GestureDetector(
      onTap: () {
        if (asset == null) {
          _selectBackground();
        } else {
          setState(() => _selectedBackground = asset);
        }
      },
      child: Container(
        width: 80,
        margin: const EdgeInsets.only(right: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? theme.colorScheme.primary : Colors.transparent,
            width: 2,
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(6),
          child: asset != null
              ? Image.file(
                  File(asset.localPath),
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: theme.colorScheme.surfaceContainerHighest,
                      child: const Icon(Icons.broken_image),
                    );
                  },
                )
              : Container(
                  color: theme.colorScheme.surfaceContainerHighest,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(icon, size: 24),
                      const SizedBox(height: 4),
                      Text(
                        label,
                        style: theme.textTheme.bodySmall,
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildTextControls(BuildContext context) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Text Style',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        
        // Text color
        Row(
          children: [
            const Text('Color: '),
            const SizedBox(width: 8),
            ...Colors.primaries.take(6).map((color) => GestureDetector(
              onTap: () => setState(() => _textColor = color),
              child: Container(
                width: 32,
                height: 32,
                margin: const EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: _textColor == color ? Colors.black : Colors.transparent,
                    width: 2,
                  ),
                ),
              ),
            )),
            GestureDetector(
              onTap: () => setState(() => _textColor = Colors.white),
              child: Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: _textColor == Colors.white ? Colors.black : Colors.grey,
                    width: 2,
                  ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        
        // Font size
        Row(
          children: [
            const Text('Size: '),
            Expanded(
              child: Slider(
                value: _fontSize,
                min: 16,
                max: 48,
                divisions: 16,
                label: _fontSize.round().toString(),
                onChanged: (value) => setState(() => _fontSize = value),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildOverlayControls(BuildContext context) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Overlay',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            Switch(
              value: _showOverlay,
              onChanged: (value) => setState(() => _showOverlay = value),
            ),
          ],
        ),
        
        if (_showOverlay) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              const Text('Color: '),
              const SizedBox(width: 8),
              ...[Colors.black, Colors.white, Colors.blue, Colors.red].map((color) => GestureDetector(
                onTap: () => setState(() => _overlayColor = color),
                child: Container(
                  width: 32,
                  height: 32,
                  margin: const EdgeInsets.only(right: 8),
                  decoration: BoxDecoration(
                    color: color,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: _overlayColor == color ? theme.colorScheme.primary : Colors.transparent,
                      width: 2,
                    ),
                  ),
                ),
              )),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              const Text('Opacity: '),
              Expanded(
                child: Slider(
                  value: _overlayOpacity,
                  min: 0.0,
                  max: 0.8,
                  divisions: 8,
                  label: (_overlayOpacity * 100).round().toString() + '%',
                  onChanged: (value) => setState(() => _overlayOpacity = value),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  void _selectBackground() async {
    final result = await Navigator.of(context).push<LocalAsset>(
      MaterialPageRoute(
        builder: (context) => const AssetBrowserScreen(
          initialCategory: AssetCategory.backgrounds,
          selectionMode: true,
        ),
      ),
    );
    
    if (result != null) {
      setState(() => _selectedBackground = result);
    }
  }

  void _shareQuoteImage() async {
    if (_selectedBackground == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a background image first')),
      );
      return;
    }

    try {
      await SharingService.shareQuoteWithImage(
        widget.quote,
        _selectedBackground!,
        textColor: _textColor,
        backgroundColor: _overlayColor,
        fontSize: _fontSize,
      );
      
      widget.onShared?.call();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to share: $e')),
      );
    }
  }

  void _saveQuoteImage() async {
    if (_selectedBackground == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a background image first')),
      );
      return;
    }

    try {
      await SharingService.saveQuoteImageToGallery(
        widget.quote,
        _selectedBackground!,
        textColor: _textColor,
        backgroundColor: _overlayColor,
        fontSize: _fontSize,
      );
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Quote image saved to gallery')),
      );
      
      widget.onSaved?.call();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to save: $e')),
      );
    }
  }
}
