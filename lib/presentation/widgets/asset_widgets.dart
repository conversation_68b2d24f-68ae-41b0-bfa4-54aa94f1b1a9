import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../core/models/asset_models.dart';
import '../../core/services/sharing_service.dart';

/// Widget for displaying a single asset with preview and actions
class AssetCard extends StatelessWidget {
  final LocalAsset asset;
  final VoidCallback? onTap;
  final VoidCallback? onFavorite;
  final VoidCallback? onShare;
  final VoidCallback? onDelete;
  final bool showActions;
  final bool isSelected;

  const AssetCard({
    super.key,
    required this.asset,
    this.onTap,
    this.onFavorite,
    this.onShare,
    this.onDelete,
    this.showActions = true,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      elevation: isSelected ? 8 : 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isSelected
            ? BorderSide(color: theme.colorScheme.primary, width: 2)
            : BorderSide.none,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Image preview
            Expanded(
              child: ClipRRect(
                borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                child: Stack(
                  children: [
                    // Image
                    Image.file(
                      File(asset.localPath),
                      fit: BoxFit.cover,
                      width: double.infinity,
                      height: double.infinity,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: theme.colorScheme.surfaceContainerHighest,
                          child: Icon(
                            Icons.broken_image,
                            color: theme.colorScheme.onSurfaceVariant,
                            size: 48,
                          ),
                        );
                      },
                    ),
                    
                    // Favorite indicator
                    if (asset.isFavorite)
                      Positioned(
                        top: 8,
                        right: 8,
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.red.withOpacity(0.8),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.favorite,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                      ),
                    
                    // Selection indicator
                    if (isSelected)
                      Positioned(
                        top: 8,
                        left: 8,
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primary,
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.check,
                            color: theme.colorScheme.onPrimary,
                            size: 16,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
            
            // Asset info
            Padding(
              padding: const EdgeInsets.all(8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    asset.metadata?.title ?? asset.fileName,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        Icons.category,
                        size: 12,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        asset.category.displayName,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        asset.fileSizeFormatted,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                  
                  // Actions
                  if (showActions) ...[
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        _ActionButton(
                          icon: asset.isFavorite ? Icons.favorite : Icons.favorite_border,
                          onPressed: onFavorite,
                          color: asset.isFavorite ? Colors.red : null,
                        ),
                        _ActionButton(
                          icon: Icons.share,
                          onPressed: onShare ?? () => SharingService.shareImage(asset),
                        ),
                        _ActionButton(
                          icon: Icons.delete_outline,
                          onPressed: onDelete,
                          color: theme.colorScheme.error,
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    ).animate().fadeIn(duration: 300.ms).slideY(begin: 0.1, end: 0);
  }
}

/// Small action button for asset cards
class _ActionButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback? onPressed;
  final Color? color;

  const _ActionButton({
    required this.icon,
    this.onPressed,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(20),
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Icon(
          icon,
          size: 20,
          color: color ?? theme.colorScheme.onSurfaceVariant,
        ),
      ),
    );
  }
}

/// Grid view for displaying multiple assets
class AssetGrid extends StatelessWidget {
  final List<LocalAsset> assets;
  final Function(LocalAsset)? onAssetTap;
  final Function(LocalAsset)? onAssetFavorite;
  final Function(LocalAsset)? onAssetShare;
  final Function(LocalAsset)? onAssetDelete;
  final Set<String>? selectedAssetIds;
  final bool showActions;
  final int crossAxisCount;

  const AssetGrid({
    super.key,
    required this.assets,
    this.onAssetTap,
    this.onAssetFavorite,
    this.onAssetShare,
    this.onAssetDelete,
    this.selectedAssetIds,
    this.showActions = true,
    this.crossAxisCount = 2,
  });

  @override
  Widget build(BuildContext context) {
    if (assets.isEmpty) {
      return const _EmptyAssetsView();
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 0.8,
      ),
      itemCount: assets.length,
      itemBuilder: (context, index) {
        final asset = assets[index];
        final isSelected = selectedAssetIds?.contains(asset.id) ?? false;
        
        return AssetCard(
          asset: asset,
          onTap: () => onAssetTap?.call(asset),
          onFavorite: () => onAssetFavorite?.call(asset),
          onShare: () => onAssetShare?.call(asset),
          onDelete: () => onAssetDelete?.call(asset),
          showActions: showActions,
          isSelected: isSelected,
        );
      },
    );
  }
}

/// Widget for category selection
class CategorySelector extends StatelessWidget {
  final AssetCategory? selectedCategory;
  final Function(AssetCategory?) onCategoryChanged;
  final bool showAllOption;

  const CategorySelector({
    super.key,
    this.selectedCategory,
    required this.onCategoryChanged,
    this.showAllOption = true,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 50,
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        children: [
          if (showAllOption)
            _CategoryChip(
              label: 'All',
              icon: Icons.apps,
              isSelected: selectedCategory == null,
              onTap: () => onCategoryChanged(null),
            ),
          
          ...AssetCategory.values.map((category) => _CategoryChip(
            label: category.displayName,
            icon: _getIconForCategory(category),
            isSelected: selectedCategory == category,
            onTap: () => onCategoryChanged(category),
          )),
        ],
      ),
    );
  }

  IconData _getIconForCategory(AssetCategory category) {
    switch (category) {
      case AssetCategory.backgrounds:
        return Icons.image;
      case AssetCategory.motivational:
        return Icons.trending_up;
      case AssetCategory.nature:
        return Icons.nature;
      case AssetCategory.abstract:
        return Icons.palette;
      case AssetCategory.lifestyle:
        return Icons.spa;
      case AssetCategory.achievements:
        return Icons.emoji_events;
      case AssetCategory.avatars:
        return Icons.account_circle;
    }
  }
}

/// Individual category chip
class _CategoryChip extends StatelessWidget {
  final String label;
  final IconData icon;
  final bool isSelected;
  final VoidCallback onTap;

  const _CategoryChip({
    required this.label,
    required this.icon,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 16),
            const SizedBox(width: 4),
            Text(label),
          ],
        ),
        selected: isSelected,
        onSelected: (_) => onTap(),
        backgroundColor: theme.colorScheme.surface,
        selectedColor: theme.colorScheme.primaryContainer,
        checkmarkColor: theme.colorScheme.onPrimaryContainer,
      ),
    );
  }
}

/// Empty state for when no assets are available
class _EmptyAssetsView extends StatelessWidget {
  const _EmptyAssetsView();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.photo_library_outlined,
            size: 64,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 16),
          Text(
            'No Images Available',
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Download some beautiful images to get started',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              // Trigger asset download
            },
            icon: const Icon(Icons.download),
            label: const Text('Download Images'),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 500.ms);
  }
}
