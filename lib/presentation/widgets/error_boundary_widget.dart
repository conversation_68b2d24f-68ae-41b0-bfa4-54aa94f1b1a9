import 'dart:ui';

import 'package:flutter/material.dart';
import '../../core/services/logging_service.dart';
import '../../core/utils/platform_utils.dart';

/// Production-level error boundary widget that catches and handles errors gracefully
class ErrorBoundary extends StatefulWidget {
  final Widget child;
  final Widget? fallback;
  final String? errorTitle;
  final String? errorMessage;
  final VoidCallback? onError;
  final bool showErrorDetails;

  const ErrorBoundary({
    super.key,
    required this.child,
    this.fallback,
    this.errorTitle,
    this.errorMessage,
    this.onError,
    this.showErrorDetails = false,
  });

  @override
  State<ErrorBoundary> createState() => _ErrorBoundaryState();
}

class _ErrorBoundaryState extends State<ErrorBoundary> {
  Object? _error;
  StackTrace? _stackTrace;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();

    // Set up error handling for this widget tree
    FlutterError.onError = (FlutterErrorDetails details) {
      if (mounted) {
        _handleError(details.exception, details.stack);
      }
    };
  }

  @override
  Widget build(BuildContext context) {
    if (_hasError) {
      return widget.fallback ?? _buildErrorWidget(context);
    }

    // Wrap the child in an error-catching widget
    return _ErrorCatcher(
      onError: _handleError,
      child: widget.child,
    );
  }

  void _handleError(Object error, StackTrace? stackTrace) {
    setState(() {
      _error = error;
      _stackTrace = stackTrace;
      _hasError = true;
    });

    // Log the error
    LoggingService.error(
      'Error caught by ErrorBoundary: ${error.toString()}',
      error: error,
      stackTrace: stackTrace,
    );

    // Call custom error handler if provided
    widget.onError?.call();
  }

  Widget _buildErrorWidget(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.colorScheme.errorContainer.withOpacity(0.1),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: theme.colorScheme.error,
              ),
              const SizedBox(height: 24),
              Text(
                widget.errorTitle ?? 'Oops! Something went wrong',
                style: theme.textTheme.headlineSmall?.copyWith(
                  color: theme.colorScheme.error,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                widget.errorMessage ?? 
                'We encountered an unexpected error. Don\'t worry, your data is safe.',
                style: theme.textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              
              // Action buttons
              Column(
                children: [
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _restartApp,
                      icon: const Icon(Icons.refresh),
                      label: const Text('Restart App'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: theme.colorScheme.primary,
                        foregroundColor: theme.colorScheme.onPrimary,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton.icon(
                      onPressed: _reportError,
                      icon: const Icon(Icons.bug_report),
                      label: const Text('Report Issue'),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                    ),
                  ),
                  if (widget.showErrorDetails || PlatformUtils.isDebugMode) ...[
                    const SizedBox(height: 12),
                    TextButton.icon(
                      onPressed: _showErrorDetails,
                      icon: const Icon(Icons.info_outline),
                      label: const Text('Show Details'),
                    ),
                  ],
                ],
              ),
              
              const SizedBox(height: 32),
              
              // App info
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surface,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: theme.colorScheme.outline.withOpacity(0.2),
                  ),
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info,
                          size: 16,
                          color: theme.colorScheme.onSurface.withOpacity(0.6),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Error Information',
                          style: theme.textTheme.labelMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    _buildInfoRow('Platform', PlatformUtils.platformName),
                    _buildInfoRow('Time', DateTime.now().toString().split('.')[0]),
                    if (_error != null)
                      _buildInfoRow('Error Type', _error.runtimeType.toString()),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
          Flexible(
            child: Text(
              value,
              style: theme.textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }

  void _restartApp() {
    // Reset error state
    setState(() {
      _hasError = false;
      _error = null;
      _stackTrace = null;
    });

    // Navigate to home and clear stack
    Navigator.of(context).pushNamedAndRemoveUntil('/', (route) => false);
    
    LoggingService.logUserAction('error_boundary_restart');
  }

  void _reportError() {
    // In a production app, this would send error reports to a service
    LoggingService.logUserAction('error_boundary_report', data: {
      'error': _error?.toString(),
      'platform': PlatformUtils.platformName,
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Error report sent. Thank you for helping us improve!'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showErrorDetails() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error Details'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (_error != null) ...[
                const Text(
                  'Error:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                SelectableText(
                  _error.toString(),
                  style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
                ),
                const SizedBox(height: 16),
              ],
              if (_stackTrace != null) ...[
                const Text(
                  'Stack Trace:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                SelectableText(
                  _stackTrace.toString(),
                  style: const TextStyle(fontFamily: 'monospace', fontSize: 10),
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          TextButton(
            onPressed: () {
              // Copy error details to clipboard
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Error details copied to clipboard'),
                ),
              );
            },
            child: const Text('Copy'),
          ),
        ],
      ),
    );
  }
}

/// Wrapper widget that provides error boundary functionality
class ErrorBoundaryWrapper extends StatelessWidget {
  final Widget child;
  final String? title;
  final String? message;

  const ErrorBoundaryWrapper({
    super.key,
    required this.child,
    this.title,
    this.message,
  });

  @override
  Widget build(BuildContext context) {
    return ErrorBoundary(
      errorTitle: title,
      errorMessage: message,
      showErrorDetails: PlatformUtils.isDebugMode,
      child: child,
    );
  }
}

/// Internal widget that catches errors in its child widget tree
class _ErrorCatcher extends StatefulWidget {
  final Widget child;
  final Function(Object error, StackTrace? stackTrace) onError;

  const _ErrorCatcher({
    required this.child,
    required this.onError,
  });

  @override
  State<_ErrorCatcher> createState() => _ErrorCatcherState();
}

class _ErrorCatcherState extends State<_ErrorCatcher> {
  @override
  Widget build(BuildContext context) {
    return Builder(
      builder: (context) {
        try {
          return widget.child;
        } catch (error, stackTrace) {
          // Catch synchronous errors
          WidgetsBinding.instance.addPostFrameCallback((_) {
            widget.onError(error, stackTrace);
          });
          return const SizedBox.shrink();
        }
      },
    );
  }
}

/// Global error handler for the entire app
class GlobalErrorHandler {
  static void initialize() {
    // Set up global error handling
    FlutterError.onError = (FlutterErrorDetails details) {
      LoggingService.error(
        'Global Flutter Error: ${details.exception}',
        error: details.exception,
        stackTrace: details.stack,
      );

      // In debug mode, show the red screen
      if (PlatformUtils.isDebugMode) {
        FlutterError.presentError(details);
      }
    };

    // Handle errors outside of Flutter
    PlatformDispatcher.instance.onError = (error, stack) {
      LoggingService.error(
        'Global Platform Error: $error',
        error: error,
        stackTrace: stack,
      );
      return true;
    };
  }
}
