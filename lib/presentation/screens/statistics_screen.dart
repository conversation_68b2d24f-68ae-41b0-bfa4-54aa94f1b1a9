import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/analytics_provider.dart';
import '../providers/gamification_provider.dart';
import '../providers/preferences_provider.dart';
import '../../core/theme/app_theme.dart';

class StatisticsScreen extends StatefulWidget {
  const StatisticsScreen({super.key});

  @override
  State<StatisticsScreen> createState() => _StatisticsScreenState();
}

class _StatisticsScreenState extends State<StatisticsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final customColors = theme.customColors;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Your Progress'),
        backgroundColor: customColors.primaryGradient.first,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: 'Overview', icon: Icon(Icons.dashboard)),
            Tab(text: 'Reading', icon: Icon(Icons.book)),
            Tab(text: 'Achievements', icon: Icon(Icons.emoji_events)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(),
          _buildReadingTab(),
          _buildAchievementsTab(),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return Consumer3<AnalyticsProvider, GamificationProvider, PreferencesProvider>(
      builder: (context, analytics, gamification, preferences, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildStatsCards(analytics, gamification),
              const SizedBox(height: 24),
              _buildStreakChart(analytics),
              const SizedBox(height: 24),
              _buildCategoryBreakdown(analytics),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatsCards(AnalyticsProvider analytics, GamificationProvider gamification) {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'Total Quotes',
            analytics.totalQuotesViewed.toString(),
            Icons.format_quote,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'Current Streak',
            '${analytics.currentStreak} days',
            Icons.local_fire_department,
            Colors.orange,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'Level',
            gamification.level.toString(),
            Icons.star,
            Colors.purple,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStreakChart(AnalyticsProvider analytics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Reading Streak Progress',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildStreakVisualizer(analytics),
          ],
        ),
      ),
    );
  }

  Widget _buildStreakVisualizer(AnalyticsProvider analytics) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: List.generate(7, (index) {
            final dayValue = (analytics.currentStreak - (6 - index)).clamp(0, analytics.currentStreak);
            final isActive = dayValue > 0;

            return Column(
              children: [
                Container(
                  width: 30,
                  height: 60,
                  decoration: BoxDecoration(
                    color: isActive ? Colors.blue : Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: isActive
                      ? const Icon(Icons.check, color: Colors.white, size: 16)
                      : null,
                ),
                const SizedBox(height: 4),
                Text(
                  'Day ${index + 1}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            );
          }),
        ),
        const SizedBox(height: 16),
        Text(
          'Current streak: ${analytics.currentStreak} days',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.blue,
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryBreakdown(AnalyticsProvider analytics) {
    final categoryStats = analytics.categoryStats;
    final sortedCategories = categoryStats.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Category Breakdown',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...sortedCategories.take(5).map((entry) =>
              _buildCategoryProgressBar(entry.key, entry.value, categoryStats.values.reduce((a, b) => a > b ? a : b))
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryProgressBar(String category, int count, int maxCount) {
    final percentage = maxCount > 0 ? count / maxCount : 0.0;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                category.substring(0, 1).toUpperCase() + category.substring(1),
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              Text(
                count.toString(),
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          LinearProgressIndicator(
            value: percentage,
            backgroundColor: Colors.grey.shade300,
            valueColor: AlwaysStoppedAnimation<Color>(
              _getCategoryColor(category),
            ),
          ),
        ],
      ),
    );
  }

  Color _getCategoryColor(String category) {
    final colors = [
      Colors.blue, Colors.red, Colors.green, Colors.orange,
      Colors.purple, Colors.teal, Colors.pink, Colors.indigo,
    ];
    return colors[category.hashCode.abs() % colors.length];
  }

  Widget _buildReadingTab() {
    return Consumer<AnalyticsProvider>(
      builder: (context, analytics, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildReadingHabits(analytics),
              const SizedBox(height: 24),
              _buildFavoriteCategories(analytics),
              const SizedBox(height: 24),
              _buildReadingTimes(analytics),
            ],
          ),
        );
      },
    );
  }

  Widget _buildReadingHabits(AnalyticsProvider analytics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Reading Habits',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildHabitItem('Total Quotes Read', analytics.totalQuotesViewed.toString()),
            _buildHabitItem('Current Streak', '${analytics.currentStreak} days'),
            _buildHabitItem('Longest Streak', '${analytics.longestStreak} days'),
            _buildHabitItem('Average per Day', (analytics.totalQuotesViewed / (analytics.currentStreak.clamp(1, double.infinity))).toStringAsFixed(1)),
          ],
        ),
      ),
    );
  }

  Widget _buildHabitItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildFavoriteCategories(AnalyticsProvider analytics) {
    final sortedCategories = analytics.categoryStats.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Favorite Categories',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...sortedCategories.take(5).map((entry) => 
              _buildCategoryItem(entry.key, entry.value)
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryItem(String category, int count) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Expanded(
            child: Text(
              category.substring(0, 1).toUpperCase() + category.substring(1),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              count.toString(),
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onPrimaryContainer,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReadingTimes(AnalyticsProvider analytics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Reading Times',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Most Active Time: ${_getMostActiveTime(analytics)}',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 8),
            Text(
              'Total Reading Sessions: ${analytics.totalQuotesViewed}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  String _getMostActiveTime(AnalyticsProvider analytics) {
    // Simple logic to determine most active time based on current time patterns
    final now = DateTime.now();
    if (now.hour >= 6 && now.hour < 12) {
      return 'Morning (6 AM - 12 PM)';
    } else if (now.hour >= 12 && now.hour < 18) {
      return 'Afternoon (12 PM - 6 PM)';
    } else {
      return 'Evening (6 PM - 12 AM)';
    }
  }

  Widget _buildAchievementsTab() {
    return Consumer<GamificationProvider>(
      builder: (context, gamification, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildLevelProgress(gamification),
              const SizedBox(height: 24),
              _buildAchievementsList(gamification),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLevelProgress(GamificationProvider gamification) {
    final progress = gamification.experiencePoints / gamification.experienceToNextLevel;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Level ${gamification.level}',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${gamification.experiencePoints}/${gamification.experienceToNextLevel} XP',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
            const SizedBox(height: 12),
            LinearProgressIndicator(
              value: progress,
              backgroundColor: Colors.grey.shade300,
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '${gamification.experienceToNextLevel - gamification.experiencePoints} XP to next level',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAchievementsList(GamificationProvider gamification) {
    final achievements = gamification.achievements;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Achievements',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...achievements.values.map((achievement) =>
              _buildAchievementItem(achievement, gamification)
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAchievementItem(Achievement achievement, GamificationProvider gamification) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          gamification.getAchievementWidget(achievement.type, 40),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  achievement.title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: achievement.isUnlocked 
                        ? Theme.of(context).colorScheme.onSurface
                        : Colors.grey,
                  ),
                ),
                Text(
                  achievement.description,
                  style: TextStyle(
                    fontSize: 12,
                    color: achievement.isUnlocked 
                        ? Theme.of(context).colorScheme.onSurface.withOpacity(0.7)
                        : Colors.grey,
                  ),
                ),
              ],
            ),
          ),
          if (achievement.isUnlocked)
            const Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 20,
            ),
        ],
      ),
    );
  }
}
