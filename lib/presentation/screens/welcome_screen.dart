import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../widgets/common/custom_button.dart';

class WelcomeScreen extends StatelessWidget {
  const WelcomeScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).primaryColor.withOpacity(0.8),
              Theme.of(context).primaryColor.withOpacity(0.6),
              Theme.of(context).colorScheme.secondary.withOpacity(0.8),
            ],
          ),
        ),
        child: <PERSON><PERSON><PERSON>(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: <PERSON>umn(
              children: [
                const Spacer(),
                
                // App Logo and Title
                Icon(
                  Icons.format_quote,
                  size: 120,
                  color: Colors.white,
                ),
                const SizedBox(height: 24),
                
                Text(
                  'Daily Motivator',
                  style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                
                Text(
                  'Get inspired every day with motivational quotes',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.white.withOpacity(0.9),
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const Spacer(),
                
                // Features List
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    children: [
                      _buildFeatureItem(
                        icon: Icons.auto_awesome,
                        title: 'Daily Inspiration',
                        subtitle: 'Fresh motivational quotes every day',
                      ),
                      const SizedBox(height: 16),
                      _buildFeatureItem(
                        icon: Icons.category,
                        title: 'Multiple Categories',
                        subtitle: 'Success, Wisdom, Happiness & more',
                      ),
                      const SizedBox(height: 16),
                      _buildFeatureItem(
                        icon: Icons.notifications,
                        title: 'Smart Notifications',
                        subtitle: 'Customizable reminders',
                      ),
                      const SizedBox(height: 16),
                      _buildFeatureItem(
                        icon: Icons.offline_bolt,
                        title: 'Works Offline',
                        subtitle: 'No internet? No problem!',
                      ),
                    ],
                  ),
                ),
                
                const Spacer(),
                
                // Action Buttons
                Consumer<AuthProvider>(
                  builder: (context, authProvider, child) {
                    return Column(
                      children: [
                        // Continue as Guest Button
                        SizedBox(
                          width: double.infinity,
                          child: CustomButton(
                            text: 'Continue as Guest',
                            icon: Icons.person_outline,
                            onPressed: authProvider.isLoading ? null : () {
                              authProvider.continueAsGuest();
                              Navigator.of(context).pushReplacementNamed('/home');
                            },
                            isLoading: authProvider.isLoading,
                            backgroundColor: Colors.white,
                            textColor: Theme.of(context).primaryColor,
                          ),
                        ),
                        
                        const SizedBox(height: 16),
                        
                        // Login with Gmail Button
                        SizedBox(
                          width: double.infinity,
                          child: CustomButton(
                            text: 'Sign in with Gmail',
                            icon: Icons.email,
                            onPressed: authProvider.isLoading ? null : () async {
                              final success = await authProvider.loginWithGmail();
                              if (success && context.mounted) {
                                Navigator.of(context).pushReplacementNamed('/home');
                              }
                            },
                            isLoading: authProvider.isLoading,
                            isOutlined: true,
                            backgroundColor: Colors.transparent,
                            textColor: Colors.white,
                          ),
                        ),
                        
                        const SizedBox(height: 24),
                        
                        // Info Text
                        Text(
                          'Sign in to sync your data across devices',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.white.withOpacity(0.8),
                          ),
                          textAlign: TextAlign.center,
                        ),
                        
                        if (authProvider.errorMessage != null) ...[
                          const SizedBox(height: 16),
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.red.shade100,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.red.shade300),
                            ),
                            child: Row(
                              children: [
                                Icon(Icons.error, color: Colors.red.shade700, size: 20),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    authProvider.errorMessage!,
                                    style: TextStyle(color: Colors.red.shade700),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ],
                    );
                  },
                ),
                
                const SizedBox(height: 24),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureItem({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: Colors.white,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
              Text(
                subtitle,
                style: TextStyle(
                  color: Colors.white.withOpacity(0.8),
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
