import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../widgets/common/custom_button.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Profile'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          if (authProvider.isGuest) {
            return _buildGuestProfile(context, authProvider);
          } else {
            return _buildAuthenticatedProfile(context, authProvider);
          }
        },
      ),
    );
  }

  Widget _buildGuestProfile(BuildContext context, AuthProvider authProvider) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        children: [
          // Guest Avatar
          CircleAvatar(
            radius: 50,
            backgroundColor: Theme.of(context).primaryColor.withOpacity(0.1),
            child: Icon(
              Icons.person_outline,
              size: 50,
              color: Theme.of(context).primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          
          Text(
            'Guest User',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          
          Text(
            'You\'re using Daily Motivator as a guest',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 32),
          
          // Benefits Card
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.cloud_sync,
                        color: Theme.of(context).primaryColor,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'Sign in to unlock',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  
                  _buildBenefitItem(
                    icon: Icons.sync,
                    title: 'Sync across devices',
                    subtitle: 'Access your data on any device',
                  ),
                  const SizedBox(height: 12),
                  
                  _buildBenefitItem(
                    icon: Icons.backup,
                    title: 'Cloud backup',
                    subtitle: 'Never lose your preferences',
                  ),
                  const SizedBox(height: 12),
                  
                  _buildBenefitItem(
                    icon: Icons.favorite,
                    title: 'Save favorites',
                    subtitle: 'Keep your favorite quotes',
                  ),
                  const SizedBox(height: 12),
                  
                  _buildBenefitItem(
                    icon: Icons.analytics,
                    title: 'Personal insights',
                    subtitle: 'Track your motivation journey',
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 32),
          
          // Sign in Button
          SizedBox(
            width: double.infinity,
            child: CustomButton(
              text: 'Sign in with Gmail',
              icon: Icons.email,
              onPressed: authProvider.isLoading ? null : () async {
                final success = await authProvider.loginWithGmail();
                if (success && context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Successfully signed in!'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              },
              isLoading: authProvider.isLoading,
            ),
          ),
          
          const SizedBox(height: 16),
          
          Text(
            'Your data will remain on this device until you sign in',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          
          const Spacer(),
          
          // Continue as Guest
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Continue as guest'),
          ),
        ],
      ),
    );
  }

  Widget _buildAuthenticatedProfile(BuildContext context, AuthProvider authProvider) {
    final user = authProvider.currentUser!;
    
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        children: [
          // User Avatar
          CircleAvatar(
            radius: 50,
            backgroundColor: Theme.of(context).primaryColor.withOpacity(0.1),
            child: Text(
              (user['full_name'] as String? ?? 'U')[0].toUpperCase(),
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
          ),
          const SizedBox(height: 16),
          
          Text(
            user['full_name'] as String? ?? 'User',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          
          Text(
            user['email'] as String? ?? '',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          
          const SizedBox(height: 32),
          
          // Stats Card
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  Text(
                    'Your Stats',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildStatItem(
                        context,
                        icon: Icons.format_quote,
                        value: '42',
                        label: 'Quotes Read',
                      ),
                      _buildStatItem(
                        context,
                        icon: Icons.favorite,
                        value: '12',
                        label: 'Favorites',
                      ),
                      _buildStatItem(
                        context,
                        icon: Icons.local_fire_department,
                        value: '7',
                        label: 'Day Streak',
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 32),
          
          // Settings Options
          _buildSettingsOption(
            context,
            icon: Icons.sync,
            title: 'Sync Status',
            subtitle: 'Data synced to cloud',
            trailing: const Icon(Icons.cloud_done, color: Colors.green),
          ),
          
          _buildSettingsOption(
            context,
            icon: Icons.notifications,
            title: 'Notifications',
            subtitle: 'Manage your reminders',
            onTap: () {
              // Navigate to notification settings
            },
          ),
          
          _buildSettingsOption(
            context,
            icon: Icons.backup,
            title: 'Data & Privacy',
            subtitle: 'Manage your data',
            onTap: () {
              // Navigate to data settings
            },
          ),
          
          const Spacer(),
          
          // Sign Out Button
          SizedBox(
            width: double.infinity,
            child: CustomButton(
              text: 'Sign Out',
              icon: Icons.logout,
              onPressed: authProvider.isLoading ? null : () async {
                await authProvider.logout();
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Signed out successfully'),
                    ),
                  );
                  Navigator.of(context).pop();
                }
              },
              isLoading: authProvider.isLoading,
              isOutlined: true,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBenefitItem({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Row(
      children: [
        Icon(icon, size: 20, color: Colors.grey[600]),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
              ),
              Text(
                subtitle,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatItem(
    BuildContext context, {
    required IconData icon,
    required String value,
    required String label,
  }) {
    return Column(
      children: [
        Icon(icon, color: Theme.of(context).primaryColor),
        const SizedBox(height: 8),
        Text(
          value,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).primaryColor,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildSettingsOption(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return ListTile(
      leading: Icon(icon),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: trailing ?? const Icon(Icons.chevron_right),
      onTap: onTap,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
    );
  }
}
