import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/services/widget_service.dart';
import '../../core/services/offline_sync_service.dart';
import '../../core/services/api_service.dart';
import '../../domain/entities/category.dart';
import '../providers/preferences_provider.dart';

class WidgetConfigScreen extends StatefulWidget {
  const WidgetConfigScreen({super.key});

  @override
  State<WidgetConfigScreen> createState() => _WidgetConfigScreenState();
}

class _WidgetConfigScreenState extends State<WidgetConfigScreen> {
  late WidgetService _widgetService;
  late OfflineSyncService _offlineService;
  
  WidgetConfig? _config;
  List<Category> _categories = [];
  bool _isLoading = true;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    // Create services directly since they're not in providers yet
    final apiService = ApiService();
    await apiService.initialize();

    _offlineService = OfflineSyncService(apiService: apiService);
    _widgetService = WidgetService(
      apiService: apiService,
      offlineService: _offlineService,
    );

    await _loadData();
  }

  Future<void> _loadData() async {
    try {
      final config = await _widgetService.getWidgetConfig();
      final categories = await _offlineService.getStoredCategories();
      
      setState(() {
        _config = config;
        _categories = categories;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading configuration: $e')),
        );
      }
    }
  }

  Future<void> _saveConfig() async {
    if (_config == null) return;
    
    setState(() {
      _isSaving = true;
    });

    try {
      await _widgetService.updateWidgetConfig(_config!);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Widget configuration saved!')),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving configuration: $e')),
        );
      }
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Widget Settings'),
        actions: [
          if (!_isLoading && _config != null)
            TextButton(
              onPressed: _isSaving ? null : _saveConfig,
              child: _isSaving 
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Save'),
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _config == null
              ? const Center(child: Text('Failed to load configuration'))
              : _buildConfigForm(theme),
    );
  }

  Widget _buildConfigForm(ThemeData theme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildRefreshIntervalSection(theme),
          const SizedBox(height: 24),
          _buildCategoryFilterSection(theme),
          const SizedBox(height: 24),
          _buildDisplayOptionsSection(theme),
          const SizedBox(height: 24),
          _buildDataSourceSection(theme),
          const SizedBox(height: 24),
          _buildPreviewSection(theme),
        ],
      ),
    );
  }

  Widget _buildRefreshIntervalSection(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Refresh Interval',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'How often the widget should update with new quotes',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<int>(
              value: _config!.refreshIntervalMinutes,
              decoration: const InputDecoration(
                labelText: 'Update every',
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem(value: 15, child: Text('15 minutes')),
                DropdownMenuItem(value: 30, child: Text('30 minutes')),
                DropdownMenuItem(value: 60, child: Text('1 hour')),
                DropdownMenuItem(value: 120, child: Text('2 hours')),
                DropdownMenuItem(value: 240, child: Text('4 hours')),
                DropdownMenuItem(value: 480, child: Text('8 hours')),
                DropdownMenuItem(value: 1440, child: Text('24 hours')),
              ],
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _config = WidgetConfig(
                      refreshIntervalMinutes: value,
                      categoryFilter: _config!.categoryFilter,
                      showAuthor: _config!.showAuthor,
                      backgroundColor: _config!.backgroundColor,
                      textColor: _config!.textColor,
                      useApi: _config!.useApi,
                    );
                  });
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryFilterSection(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Category Filter',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Show quotes from a specific category only',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String?>(
              value: _config!.categoryFilter,
              decoration: const InputDecoration(
                labelText: 'Category',
                border: OutlineInputBorder(),
              ),
              items: [
                const DropdownMenuItem<String?>(
                  value: null,
                  child: Text('All categories'),
                ),
                ..._categories.map((category) => DropdownMenuItem<String?>(
                  value: category.slug,
                  child: Row(
                    children: [
                      Text(category.icon),
                      const SizedBox(width: 8),
                      Text(category.name),
                    ],
                  ),
                )),
              ],
              onChanged: (value) {
                setState(() {
                  _config = WidgetConfig(
                    refreshIntervalMinutes: _config!.refreshIntervalMinutes,
                    categoryFilter: value,
                    showAuthor: _config!.showAuthor,
                    backgroundColor: _config!.backgroundColor,
                    textColor: _config!.textColor,
                    useApi: _config!.useApi,
                  );
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDisplayOptionsSection(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Display Options',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Show Author'),
              subtitle: const Text('Display the author name below quotes'),
              value: _config!.showAuthor,
              onChanged: (value) {
                setState(() {
                  _config = WidgetConfig(
                    refreshIntervalMinutes: _config!.refreshIntervalMinutes,
                    categoryFilter: _config!.categoryFilter,
                    showAuthor: value,
                    backgroundColor: _config!.backgroundColor,
                    textColor: _config!.textColor,
                    useApi: _config!.useApi,
                  );
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataSourceSection(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Data Source',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Use Online API'),
              subtitle: const Text('Get fresh quotes from server when available'),
              value: _config!.useApi,
              onChanged: (value) {
                setState(() {
                  _config = WidgetConfig(
                    refreshIntervalMinutes: _config!.refreshIntervalMinutes,
                    categoryFilter: _config!.categoryFilter,
                    showAuthor: _config!.showAuthor,
                    backgroundColor: _config!.backgroundColor,
                    textColor: _config!.textColor,
                    useApi: value,
                  );
                });
              },
            ),
            if (!_config!.useApi)
              Padding(
                padding: const EdgeInsets.only(left: 16, top: 8),
                child: Text(
                  'Widget will use offline quotes only',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewSection(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Widget Preview',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: const Color(0xFF4ECDC4),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _config!.categoryFilter != null 
                        ? _categories.firstWhere(
                            (c) => c.slug == _config!.categoryFilter,
                            orElse: () => Category(
                              id: 0,
                              name: 'Category',
                              slug: '',
                              description: '',
                              color: '#4ECDC4',
                              icon: '📝',
                              isActive: true,
                              sortOrder: 0,
                              createdAt: DateTime.now(),
                              updatedAt: DateTime.now(),
                            ),
                          ).name
                        : 'Motivation',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'The only way to do great work is to love what you do.',
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF333333),
                    ),
                  ),
                  const SizedBox(height: 8),
                  if (_config!.showAuthor)
                    const Text(
                      '— Steve Jobs',
                      style: TextStyle(
                        fontSize: 12,
                        color: Color(0xFF666666),
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
