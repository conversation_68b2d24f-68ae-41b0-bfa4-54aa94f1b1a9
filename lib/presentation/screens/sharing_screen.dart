import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import '../../core/models/asset_models.dart';
import '../../core/services/sharing_service.dart';
import '../../domain/entities/quote.dart';
import '../providers/asset_provider.dart';
import '../widgets/quote_image_composer.dart';
import '../screens/asset_browser_screen.dart';

/// Comprehensive sharing screen with multiple sharing options
class SharingScreen extends StatefulWidget {
  final Quote quote;

  const SharingScreen({
    super.key,
    required this.quote,
  });

  @override
  State<SharingScreen> createState() => _SharingScreenState();
}

class _SharingScreenState extends State<SharingScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  LocalAsset? _selectedBackground;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    // Load assets if not already loaded
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final assetProvider = context.read<AssetProvider>();
      if (assetProvider.allAssets.isEmpty) {
        assetProvider.loadAssets();
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Share Quote'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.text_fields), text: 'Text'),
            Tab(icon: Icon(Icons.image), text: 'Image'),
            Tab(icon: Icon(Icons.palette), text: 'Custom'),
          ],
        ),
      ),
      
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildTextSharingTab(),
          _buildImageSharingTab(),
          _buildCustomSharingTab(),
        ],
      ),
    );
  }

  Widget _buildTextSharingTab() {
    final theme = Theme.of(context);
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Quote preview
          Card(
            elevation: 4,
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  Text(
                    '"${widget.quote.text}"',
                    style: theme.textTheme.headlineSmall?.copyWith(
                      fontStyle: FontStyle.italic,
                      height: 1.4,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    '- ${widget.quote.author}',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),
          ).animate().fadeIn(duration: 500.ms).slideY(begin: 0.1, end: 0),
          
          const SizedBox(height: 24),
          
          // Sharing options
          Text(
            'Share Options',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          _buildSharingOption(
            icon: Icons.share,
            title: 'Share Text',
            subtitle: 'Share the quote as plain text',
            onTap: () => _shareQuoteText(),
          ),
          
          _buildSharingOption(
            icon: Icons.copy,
            title: 'Copy to Clipboard',
            subtitle: 'Copy the quote text to clipboard',
            onTap: () => _copyQuoteToClipboard(),
          ),
          
          _buildSharingOption(
            icon: Icons.apps,
            title: 'Share App',
            subtitle: 'Invite friends to use Daily Motivator',
            onTap: () => _shareApp(),
          ),
        ],
      ),
    );
  }

  Widget _buildImageSharingTab() {
    return Consumer<AssetProvider>(
      builder: (context, assetProvider, child) {
        if (assetProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }
        
        if (assetProvider.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Theme.of(context).colorScheme.error,
                ),
                const SizedBox(height: 16),
                Text(
                  'Error loading images',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 8),
                Text(assetProvider.error!),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => assetProvider.loadAssets(),
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }
        
        final backgrounds = assetProvider.getAssetsForCategory(AssetCategory.backgrounds);
        final motivational = assetProvider.getAssetsForCategory(AssetCategory.motivational);
        final nature = assetProvider.getAssetsForCategory(AssetCategory.nature);
        
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Quick Share with Background',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Select a background image to create and share a quote image',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: 16),
              
              if (backgrounds.isNotEmpty) ...[
                _buildImageCategorySection('Backgrounds', backgrounds),
                const SizedBox(height: 24),
              ],
              
              if (motivational.isNotEmpty) ...[
                _buildImageCategorySection('Motivational', motivational),
                const SizedBox(height: 24),
              ],
              
              if (nature.isNotEmpty) ...[
                _buildImageCategorySection('Nature', nature),
                const SizedBox(height: 24),
              ],
              
              // Browse all images button
              Card(
                child: ListTile(
                  leading: const Icon(Icons.photo_library),
                  title: const Text('Browse All Images'),
                  subtitle: const Text('View all available background images'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: _browseAllImages,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCustomSharingTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'Custom Quote Image',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create a custom quote image with full control over styling and background',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 24),
          
          Card(
            elevation: 4,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Icon(
                    Icons.palette,
                    size: 64,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Quote Image Composer',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Customize text style, colors, background, and overlay effects',
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: _openQuoteComposer,
                    icon: const Icon(Icons.edit),
                    label: const Text('Create Custom Image'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ).animate().fadeIn(duration: 500.ms).scale(begin: const Offset(0.9, 0.9)),
          
          const SizedBox(height: 24),
          
          // Quick actions
          Text(
            'Quick Actions',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          
          _buildSharingOption(
            icon: Icons.download,
            title: 'Download More Images',
            subtitle: 'Get more free background images',
            onTap: _downloadMoreImages,
          ),
          
          _buildSharingOption(
            icon: Icons.photo_library,
            title: 'Manage Gallery',
            subtitle: 'Organize your image collection',
            onTap: _manageGallery,
          ),
        ],
      ),
    );
  }

  Widget _buildImageCategorySection(String title, List<LocalAsset> assets) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: assets.length,
            itemBuilder: (context, index) {
              final asset = assets[index];
              return _buildImageOption(asset);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildImageOption(LocalAsset asset) {
    return GestureDetector(
      onTap: () => _shareQuoteWithImage(asset),
      child: Container(
        width: 120,
        margin: const EdgeInsets.only(right: 12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Stack(
            children: [
              Image.file(
                File(asset.localPath),
                fit: BoxFit.cover,
                width: double.infinity,
                height: double.infinity,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: Theme.of(context).colorScheme.surfaceContainerHighest,
                    child: const Icon(Icons.broken_image),
                  );
                },
              ),
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withOpacity(0.6),
                    ],
                  ),
                ),
              ),
              Positioned(
                bottom: 8,
                left: 8,
                right: 8,
                child: Text(
                  asset.metadata?.title ?? 'Image',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    ).animate().fadeIn(duration: 300.ms).slideX(begin: 0.1, end: 0);
  }

  Widget _buildSharingOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Theme.of(context).colorScheme.primaryContainer,
          child: Icon(
            icon,
            color: Theme.of(context).colorScheme.onPrimaryContainer,
          ),
        ),
        title: Text(title),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
      ),
    ).animate().fadeIn(duration: 300.ms).slideX(begin: 0.1, end: 0);
  }

  void _shareQuoteText() async {
    await SharingService.shareQuote(widget.quote);
    _showSuccessMessage('Quote shared successfully!');
  }

  void _copyQuoteToClipboard() async {
    await SharingService.copyQuoteToClipboard(widget.quote);
    _showSuccessMessage('Quote copied to clipboard!');
  }

  void _shareApp() async {
    await SharingService.shareApp();
  }

  void _shareQuoteWithImage(LocalAsset asset) async {
    try {
      await SharingService.shareQuoteWithImage(widget.quote, asset);
      _showSuccessMessage('Quote image shared successfully!');
    } catch (e) {
      _showErrorMessage('Failed to share quote image: $e');
    }
  }

  void _openQuoteComposer() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => QuoteImageComposer(
          quote: widget.quote,
          initialBackground: _selectedBackground,
          onShared: () => _showSuccessMessage('Quote image shared!'),
          onSaved: () => _showSuccessMessage('Quote image saved!'),
        ),
      ),
    );
  }

  void _browseAllImages() async {
    final result = await Navigator.of(context).push<LocalAsset>(
      MaterialPageRoute(
        builder: (context) => const AssetBrowserScreen(
          selectionMode: true,
        ),
      ),
    );
    
    if (result != null) {
      _shareQuoteWithImage(result);
    }
  }

  void _downloadMoreImages() {
    context.read<AssetProvider>().downloadDefaultAssets();
    _showSuccessMessage('Downloading more images...');
  }

  void _manageGallery() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const AssetBrowserScreen(),
      ),
    );
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
