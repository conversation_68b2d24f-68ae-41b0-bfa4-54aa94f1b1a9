import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/timezone.dart' as tz;
import 'package:flutter_native_timezone/flutter_native_timezone.dart';

import 'core/di/injection_container.dart'; 
import 'core/services/storage_service.dart';
import 'core/services/logging_service.dart';
import 'presentation/providers/preferences_provider.dart';
import 'presentation/providers/theme_provider.dart';
import 'presentation/providers/auth_provider.dart';
import 'presentation/screens/splash_screen.dart';
import 'presentation/screens/onboarding_screen.dart';
import 'presentation/screens/home_screen.dart';
import 'presentation/screens/auth_screen.dart';
import 'presentation/widgets/animated_loading_widget.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize production services
  await _initializeServices();

  // Initialize dependency injection
  final di = InjectionContainer();
  await di.init();

  runApp(DailyMotivatorApp(di: di));
}

/// Initialize all production services
Future<void> _initializeServices() async {
  try {
    // Initialize timezone database
    tz.initializeTimeZones();
    final String timeZoneName = await FlutterNativeTimezone.getLocalTimezone();
    tz.setLocalLocation(tz.getLocation(timeZoneName));

    // Initialize storage service
    await StorageService.initialize();

    // Initialize logging service
    await LoggingService.initialize();

    LoggingService.info('Production services initialized successfully');
  } catch (e) {
    debugPrint('Failed to initialize services: $e');
    // Continue app startup even if some services fail
  }
}

class DailyMotivatorApp extends StatelessWidget {
  final InjectionContainer di;

  const DailyMotivatorApp({super.key, required this.di});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // Core application providers
        ChangeNotifierProvider.value(value: di.authProvider),
        ChangeNotifierProvider.value(value: di.quoteProvider),
        ChangeNotifierProvider.value(value: di.preferencesProvider),
        ChangeNotifierProvider.value(value: di.notificationProvider),
        ChangeNotifierProvider.value(value: di.themeProvider),
        ChangeNotifierProvider.value(value: di.analyticsProvider),
        ChangeNotifierProvider.value(value: di.gamificationProvider),
        ChangeNotifierProvider.value(value: di.userProvider),
        ChangeNotifierProvider.value(value: di.assetProvider),
      ],
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) {
          return MaterialApp(
            title: 'Daily Motivator',
            debugShowCheckedModeBanner: false,
            theme: themeProvider.lightTheme,
            darkTheme: themeProvider.darkTheme,
            themeMode: themeProvider.materialThemeMode,
            home: const SplashScreen(),
            routes: {
              '/auth': (context) => const AuthScreen(),
              '/home': (context) => const AppRouter(),
              '/onboarding': (context) => const OnboardingScreen(),
            },
          );
        },
      ),
    );
  }
}

class AppRouter extends StatelessWidget {
  const AppRouter({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer2<AuthProvider, PreferencesProvider>(
      builder: (context, authProvider, preferencesProvider, child) {
        // Show loading while checking auth state
        if (authProvider.state == AuthState.initial || preferencesProvider.isLoading) {
          return const Scaffold(
            body: OnboardingLoadingWidget(
              message: 'Loading...',
            ),
          );
        }

        // Users can access the app in guest mode or authenticated mode
        if (authProvider.canUseApp) {
          // If onboarding not complete, show onboarding
          if (!preferencesProvider.onboardingComplete) {
            return const OnboardingScreen();
          }

          // Show home screen for both guest and authenticated users
          return const HomeScreen();
        }

        // Fallback to auth screen (should rarely happen with guest mode)
        return const AuthScreen();
      },
    );
  }
}
