# Deployment Guide for Motivational Quotes App

This guide covers deploying the motivational quotes app to Render.com with PostgreSQL database and configuring the Flutter app for offline/online functionality with Android home screen widget.

## 🚀 Backend Deployment on Render.com

### Prerequisites
- GitHub account with your code repository
- Render.com account (free tier available)

### Step 1: Prepare Your Repository
1. Push your code to GitHub
2. Ensure all backend files are in the `backend/` directory
3. Make sure `requirements.txt` includes all dependencies

### Step 2: Create PostgreSQL Database on Render.com
1. Go to [Render.com Dashboard](https://dashboard.render.com)
2. Click "New" → "PostgreSQL"
3. Configure:
   - **Name**: `motivational-quotes-db`
   - **Database**: `motivational_quotes`
   - **User**: `motivational_user`
   - **Region**: Choose closest to your users
   - **Plan**: Free (or paid for production)
4. Click "Create Database"
5. Note the connection details (will be used automatically)

### Step 3: Deploy Backend API
1. In Render.com Dashboard, click "New" → "Web Service"
2. Connect your GitHub repository
3. Configure:
   - **Name**: `motivational-quotes-api`
   - **Environment**: `Python 3`
   - **Build Command**: 
     ```bash
     pip install --upgrade pip && pip install -r requirements.txt && python scripts/migrate_to_postgres.py
     ```
   - **Start Command**: 
     ```bash
     uvicorn app.main:app --host 0.0.0.0 --port $PORT
     ```
   - **Root Directory**: `backend`

### Step 4: Configure Environment Variables
In your web service settings, add these environment variables:

```bash
# Required
ENVIRONMENT=production
DATABASE_URL=<automatically_provided_by_render>
SECRET_KEY=<generate_strong_secret>
JWT_SECRET=<generate_strong_jwt_secret>

# Optional
LOG_LEVEL=INFO
LOG_FORMAT=json
DATABASE_ECHO=false
BACKEND_CORS_ORIGINS=["*"]
```

### Step 5: Deploy
1. Click "Create Web Service"
2. Render will automatically build and deploy
3. Your API will be available at: `https://your-service-name.onrender.com`

## 📱 Flutter App Configuration

### Step 1: Update API Configuration
1. Open `lib/core/services/api_service.dart`
2. Update the `_defaultBaseUrl` to your Render.com API URL:
   ```dart
   static const String _defaultBaseUrl = 'https://your-service-name.onrender.com/api/v1';
   ```

### Step 2: Configure Offline Sync
The app automatically handles offline/online synchronization:

- **Online**: Fetches fresh quotes from API
- **Offline**: Uses locally stored quotes
- **Sync**: Automatically syncs when connection is available

### Step 3: Android Widget Setup
The app includes an Android home screen widget that:

- Displays motivational quotes
- Updates automatically (configurable interval)
- Works offline with cached quotes
- Has a refresh button for manual updates
- Can filter by category

## 🔧 Widget Configuration

### In the App:
1. Go to Settings → Widget Configuration
2. Configure:
   - **Refresh Interval**: How often to update (15 min - 24 hours)
   - **Category Filter**: Show quotes from specific category
   - **Show Author**: Display author names
   - **Data Source**: Use API or offline only

### Adding Widget to Home Screen:
1. Long press on Android home screen
2. Tap "Widgets"
3. Find "Daily Motivator"
4. Drag to home screen
5. Widget will show quotes based on your configuration

## 🛠️ Local Development

### Backend Development
```bash
cd backend

# Install dependencies
pip install -r requirements.txt

# Set up local database (optional)
docker-compose up -d db

# Run development server
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Flutter Development
```bash
# Install dependencies
flutter pub get

# Run on Android
flutter run

# Build APK
flutter build apk --release
```

## 📊 API Endpoints

### Core Endpoints
- `GET /api/v1/quotes` - Get quotes with pagination
- `GET /api/v1/categories` - Get all categories
- `POST /api/v1/sync/quotes` - Sync quotes for offline use
- `GET /api/v1/sync/offline-quotes` - Get quotes for offline storage
- `GET /api/v1/sync/widget-quote` - Get single quote for widget

### Authentication (Optional)
- `POST /api/v1/auth/register` - Register user
- `POST /api/v1/auth/login` - Login user
- `GET /api/v1/users/me` - Get current user

## 🔒 Security Considerations

### Production Security
1. **Environment Variables**: Never commit secrets to repository
2. **CORS**: Configure proper CORS origins in production
3. **Rate Limiting**: API includes rate limiting (100 requests/15 minutes)
4. **HTTPS**: Render.com provides HTTPS automatically
5. **Database**: PostgreSQL connection is encrypted

### App Security
1. **Guest Mode**: App works without authentication by default
2. **Data Storage**: Sensitive data is stored securely
3. **API Keys**: No API keys required for basic functionality

## 📈 Monitoring and Maintenance

### Health Checks
- API Health: `GET /health`
- Database: Automatic health checks in Render.com
- Widget: Automatic error handling and fallbacks

### Logs
- Render.com provides automatic logging
- Check logs in Render.com dashboard
- Flutter app logs errors locally

### Updates
1. **Backend**: Push to GitHub, Render.com auto-deploys
2. **Database**: Migrations run automatically on deploy
3. **Flutter**: Build and distribute new APK versions

## 🎯 Features Summary

### ✅ Implemented Features
- **Backend API** with PostgreSQL on Render.com
- **Offline/Online sync** for quotes and categories
- **Android home screen widget** with configurable options
- **Guest access** - no registration required
- **Category filtering** for quotes and widget
- **Automatic background updates** for widget
- **Production-ready deployment** configuration

### 🔄 Sync Behavior
- **Initial Load**: Downloads 50 featured quotes for offline use
- **Periodic Sync**: Updates quotes every 24 hours (configurable)
- **Widget Updates**: Refreshes every 1 hour (configurable)
- **Manual Refresh**: Users can manually refresh widget
- **Offline Fallback**: Always works with cached quotes

### 📱 Widget Features
- **Configurable refresh interval** (15 minutes to 24 hours)
- **Category filtering** (show quotes from specific categories)
- **Online/Offline modes** (API or cached quotes)
- **Author display toggle**
- **Manual refresh button**
- **Tap to open app**

## 🚨 Troubleshooting

### Common Issues
1. **API not responding**: Check Render.com service status
2. **Database connection**: Verify DATABASE_URL environment variable
3. **Widget not updating**: Check Android battery optimization settings
4. **Offline quotes empty**: Trigger manual sync in app settings

### Support
- Check Render.com logs for backend issues
- Use Flutter debug mode for app issues
- Verify network connectivity for sync problems
